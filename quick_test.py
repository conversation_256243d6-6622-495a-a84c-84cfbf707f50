#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع للتأكد من أن البوت يعمل مع Supabase
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_supabase_connection():
    """اختبار الاتصال بـ Supabase"""
    print("🔄 اختبار الاتصال بـ Supabase...")
    
    try:
        from supabase_client import get_mods_count, get_all_mods
        
        # اختبار الاتصال
        count = get_mods_count()
        print(f"✅ عدد المودات في قاعدة البيانات: {count}")
        
        if count > 0:
            # اختبار جلب المودات
            mods = get_all_mods()
            print(f"✅ تم جلب {len(mods)} مود بنجاح")
            return True
        else:
            print("⚠️ قاعدة البيانات فارغة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ Supabase: {e}")
        return False

def test_bot_loading():
    """اختبار تحميل المودات في البوت"""
    print("\n🔄 اختبار تحميل المودات في البوت...")
    
    try:
        from main import load_mods
        
        mods = load_mods()
        print(f"✅ تم تحميل {len(mods)} مود في البوت بنجاح")
        
        if mods:
            first_mod = mods[0]
            print(f"📋 مثال على أول مود:")
            print(f"   - المعرف: {first_mod.get('id')}")
            print(f"   - العنوان: {first_mod.get('title')}")
            print(f"   - الإصدار: {first_mod.get('version', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحميل المودات في البوت: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار سريع للبوت")
    print("=" * 60)
    
    # اختبار الاتصال بـ Supabase
    supabase_ok = test_supabase_connection()
    
    # اختبار تحميل المودات في البوت
    bot_ok = test_bot_loading()
    
    print("\n" + "=" * 60)
    if supabase_ok and bot_ok:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ البوت جاهز للعمل مع Supabase")
        print("🚀 يمكنك تشغيل البوت الآن باستخدام: python main.py")
    else:
        print("❌ فشلت بعض الاختبارات")
        if not supabase_ok:
            print("🔧 مشكلة في الاتصال بـ Supabase")
        if not bot_ok:
            print("🔧 مشكلة في تحميل المودات في البوت")
    print("=" * 60)

if __name__ == "__main__":
    main()

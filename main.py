import asyncio
import logging
import json
import os
import html # Import html module for escaping
from datetime import datetime, timezone, timedelta # Import timedelta
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ChatMemberAdministrator, ChatMemberOwner # Import necessary types
from telegram.ext import (
    Application,
    CommandHandler,
    CallbackQueryHandler,
    ContextTypes,
    MessageHandler,
    filters,
)

from telegram.error import BadRequest, TelegramError, Forbidden, NetworkError, TimedOut
import telegram # Ensure telegram is imported for type hints

# Import Supabase client
from supabase_client import get_all_mods, get_mod_by_id, delete_mod_from_db, get_mods_count, add_mod_to_db, update_mod_in_db, search_mods



# إعدادات التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO, # Changed to INFO for production, DEBUG for development
    datefmt="%Y-%m-%d %H:%M:%S",
)
# Silence noisy libraries
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("telegram.vendor.ptb_urllib3.urllib3").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

# الإعدادات الأساسية
ADMIN_SETTINGS_FILE = "admin_settings.json"
TOKEN = os.environ.get("BOT_TOKEN", "7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4") # الأفضل استخدام متغير بيئة
MODS_FILE = "mods.json"
USER_CHANNELS_FILE = "user_channels.json"
ALL_USERS_FILE = "all_users.json"  # ملف جديد لحفظ جميع المستخدمين
ADMIN_PROCESSED_MODS_FILE = "admin_processed_mods.json"
PENDING_PUBLICATION_FILE = "pending_publication.json" # Queue for mods waiting for schedule/user approval
USER_FEEDBACK_FILE = "user_feedback.json"
USER_MODS_STATUS_FILE = "user_mods_status.json"
USER_BLOCKED_MODS_FILE = "user_blocked_mods.json"
# تأكد من استبدال هذا بمعرف الدردشة الخاص بك
YOUR_CHAT_ID = os.environ.get("ADMIN_CHAT_ID", "7513880877") # الأفضل استخدام متغير بيئة
ADMIN_USERNAME = os.environ.get("ADMIN_USERNAME", "Kim880198")  # معرف المسؤول (اختياري للعرض)
USER_APPROVAL_TIMEOUT_DAYS = 1  # كم يوم ينتظر موافقة المستخدم قبل الرفض التلقائي
ADMIN_APPROVAL_TIMEOUT_DAYS = 1 # كم يوم ينتظر موافقة المسؤول قبل التخطي التلقائي
PENDING_CLEANUP_INTERVAL_HOURS = 12 # كل كم ساعة تعمل وظيفة التنظيف
INSTALL_GUIDE_URL = "https://cvkrtjvrg.blogspot.com/p/blog-page.html"
# تأكد أن YOUR_CHAT_ID هو string
if not isinstance(YOUR_CHAT_ID, str):
    YOUR_CHAT_ID = str(YOUR_CHAT_ID)

# --- الدوال المساعدة ---

async def cleanup_stale_pending_items(context: ContextTypes.DEFAULT_TYPE):
    """
    Periodically cleans up stale items from the pending publication queue.
    - Removes items for invalid/deleted users/channels.
    - Auto-rejects/skips items exceeding defined timeouts.
    """
    logger.info("--- Running cleanup_stale_pending_items job ---")
    pending_list = load_pending_publications()
    user_channels = load_user_channels() # للتحقق من صحة المستخدمين
    now = datetime.now(timezone.utc)
    items_removed_count = 0
    items_auto_processed_count = 0
    updated_pending_list = [] # سنبني قائمة جديدة بالعناصر التي يجب الاحتفاظ بها

    # حساب المهلات بالـ timedelta
    user_timeout_delta = timedelta(days=USER_APPROVAL_TIMEOUT_DAYS)
    admin_timeout_delta = timedelta(days=ADMIN_APPROVAL_TIMEOUT_DAYS)

    for item in pending_list:
        mod_id = item.get("mod_id")
        user_id_str = item.get("user_id")
        status = item.get("status")
        proposal_time_str = item.get("proposal_time")
        status_update_time_str = item.get("status_update_time")

        # 1. التحقق من صلاحية المستخدم والقناة
        user_data = user_channels.get(user_id_str)
        if not user_data or not isinstance(user_data, dict) or not user_data.get("channel_id"):
            logger.warning(f"Stale item found: User {user_id_str} or their channel not found/invalid for mod {mod_id}. Removing item.")
            items_removed_count += 1
            continue # لا تضف هذا العنصر للقائمة الجديدة

        # 2. التحقق من المهلة الزمنية
        item_timestamp_str = status_update_time_str or proposal_time_str # استخدم وقت آخر تحديث إن وجد
        if not item_timestamp_str:
            logger.warning(f"Stale item found: Missing timestamp for mod {mod_id}/user {user_id_str}. Removing item.")
            items_removed_count += 1
            continue

        try:
            item_timestamp = datetime.fromisoformat(item_timestamp_str)
            # تأكد من أن الطابع الزمني يحتوي على معلومات المنطقة الزمنية (UTC)
            if item_timestamp.tzinfo is None:
                item_timestamp = item_timestamp.replace(tzinfo=timezone.utc)

            age = now - item_timestamp

            # -- حالة انتظار موافقة المستخدم --
            if status == "awaiting_user_approval" and age > user_timeout_delta:
                logger.warning(f"Stale item found: Mod {mod_id}/user {user_id_str} awaiting user approval timed out after {age}. Auto-rejecting.")
                # إجراء الرفض التلقائي
                save_user_feedback(user_id_str, mod_id, False) # سجل كرفض
                save_published_mod(user_id_str, mod_id)      # علمه كمعالج لهذا المستخدم
                items_auto_processed_count += 1
                # إرسال إشعار اختياري للمستخدم
                try:
                    lang = user_data.get("lang", "ar")
                    timeout_msg = {
                        "ar": f"⏳ انتهت صلاحية معاينة المود (ID: {mod_id}). تم رفضه تلقائياً.",
                        "en": f"⏳ The preview for mod (ID: {mod_id}) has expired and was automatically rejected."
                    }.get(lang, "ar")
                    await context.bot.send_message(user_id_str, timeout_msg)
                except Exception as notify_err:
                    logger.error(f"Failed to notify user {user_id_str} about preview timeout for mod {mod_id}: {notify_err}")
                continue # لا تحتفظ بالعنصر في القائمة

            # -- حالة انتظار موافقة المسؤول --
            elif status == "awaiting_admin_approval" and age > admin_timeout_delta:
                logger.warning(f"Stale item found: Mod {mod_id}/user {user_id_str} awaiting admin approval timed out after {age}. Auto-skipping for user.")
                # إجراء التخطي التلقائي لهذا المستخدم
                save_published_mod(user_id_str, mod_id) # علمه كمعالج لهذا المستخدم
                items_auto_processed_count += 1
                # إرسال إشعار اختياري للمسؤول
                try:
                     await send_notification(f"⏳ تم تخطي المود {mod_id} تلقائياً للمستخدم {user_id_str} بسبب انتهاء مهلة انتظار موافقة المسؤول.", context)
                except Exception: pass # تجاهل أخطاء إشعار المسؤول
                continue # لا تحتفظ بالعنصر في القائمة

            # -- حالات أخرى (مثل خطأ غير متوقع) - يمكن إزالتها أو تركها --
            # elif status not in ["awaiting_user_approval", "awaiting_admin_approval"] and age > admin_timeout_delta: # مهلة أطول للحالات الأخرى
            #     logger.warning(f"Stale item found: Mod {mod_id}/user {user_id_str} in unexpected status '{status}' for {age}. Removing.")
            #     items_removed_count += 1
            #     continue

        except ValueError:
            logger.error(f"Stale item found: Invalid timestamp format '{item_timestamp_str}' for mod {mod_id}/user {user_id_str}. Removing item.")
            items_removed_count += 1
            continue # لا تحتفظ بالعنصر إذا كان الوقت غير صالح

        # إذا لم يتم استيفاء أي شرط للحذف أو المعالجة التلقائية، احتفظ بالعنصر
        updated_pending_list.append(item)

    # حفظ القائمة المحدثة فقط إذا حدث تغيير
    if items_removed_count > 0 or items_auto_processed_count > 0:
        logger.info(f"Cleanup finished. Items removed (invalid user/etc): {items_removed_count}, Items auto-processed (timeout): {items_auto_processed_count}.")
        save_pending_publications(updated_pending_list)
    else:
        logger.info("--- Cleanup finished. No stale items found requiring action. ---")

def load_user_blocked_mods():
    """Loads the dictionary of blocked mods per user."""
    # Stores data as: {"user_id_str": [blocked_mod_id1, blocked_mod_id2, ...]}
    return load_json_file(USER_BLOCKED_MODS_FILE, {})

def save_user_blocked_mod(user_id: str, mod_id: int):
    """Adds a mod ID to the user's blocked list."""
    blocked_data = load_user_blocked_mods()
    user_id_str = str(user_id)

    if user_id_str not in blocked_data:
        blocked_data[user_id_str] = []

    if mod_id not in blocked_data[user_id_str]:
        blocked_data[user_id_str].append(mod_id)
        save_json_file(USER_BLOCKED_MODS_FILE, blocked_data)
        logger.info(f"User {user_id_str} blocked mod {mod_id}.")
        return True
    else:
        logger.debug(f"Mod {mod_id} was already blocked by user {user_id_str}.")
        return False

def add_awaiting_admin_approval(mod_id: int, user_id: str, channel_id: str):
    """Adds a mod to the pending file with status 'awaiting_admin_approval'."""
    pending = load_pending_publications()
    user_id_str = str(user_id) # Ensure user_id is string

    # --- إضافة: تعريف وتحقق من is_already_awaiting و is_duplicate ---
    # تحقق مما إذا كان المستخدم لديه *أي* مود آخر ينتظر موافقة المسؤول
    is_already_awaiting = any(
        item.get("user_id") == user_id_str and item.get("status") == "awaiting_admin_approval"
        for item in pending
    )

    # تحقق مما إذا كان *هذا المود المحدد لهذا المستخدم* موجود بالفعل في القائمة بأي حالة
    is_duplicate = any(
        item.get("user_id") == user_id_str and item.get("mod_id") == mod_id
        for item in pending
    )
    # --- نهاية الإضافة ---

    # استخدم المتغيرات المعرفة الآن
    if is_already_awaiting:
        # يمكنك اختيار إما منع إضافة مود جديد تمامًا إذا كان هناك واحد قيد الانتظار،
        # أو السماح بإضافته ولكن تسجيل تحذير. الخيار الحالي هو المنع.
        logger.info(f"User {user_id_str} already has another mod awaiting admin approval. Skipping adding mod {mod_id}.")
        # قد ترغب في تغيير هذا السلوك إذا أردت السماح بأكثر من اقتراح معلق للمستخدم الواحد.
        return False # منع الإضافة

    if is_duplicate:
        # هذا التحقق مهم لمنع إضافة نفس المود لنفس المستخدم عدة مرات في القائمة
        logger.warning(f"Attempted to add duplicate pending entry for mod {mod_id} / user {user_id_str}. Skipping.")
        return False # منع الإضافة المكررة

    # --- تعديل: إضافة وقت الاقتراح ---
    now_iso = datetime.now(timezone.utc).isoformat()
    pending.append({
        "mod_id": mod_id,
        "user_id": user_id_str,
        "channel_id": str(channel_id) if channel_id else None,
        "status": "awaiting_admin_approval",
        "proposal_time": now_iso, # وقت الاقتراح الأولي
        "status_update_time": now_iso # وقت آخر تحديث للحالة (نفسه عند الإنشاء)
    })
    # --------------------------------
    save_pending_publications(pending)
    logger.info(f"Added mod {mod_id} for user {user_id_str} to pending queue (awaiting admin approval) at {now_iso}.")
    return True

def add_awaiting_user_approval(mod_id: int, user_id: str, channel_id: str):
    """Adds a mod to the pending file with status 'awaiting_user_approval'."""
    pending = load_pending_publications()
    user_id_str = str(user_id) # Ensure user_id is string

    # Check if this specific mod for this user is already pending with this status or awaiting admin
    is_duplicate = any(
        item.get("user_id") == user_id_str and
        item.get("mod_id") == mod_id and
        item.get("status") in ["awaiting_user_approval", "awaiting_admin_approval"] # Check both states
        for item in pending
    )

    if is_duplicate:
        logger.warning(f"Attempted to add duplicate pending entry (awaiting user) for mod {mod_id} / user {user_id_str}. Skipping.")
        return False # Prevent duplicate

    now_iso = datetime.now(timezone.utc).isoformat()
    pending.append({
        "mod_id": mod_id,
        "user_id": user_id_str,
        "channel_id": str(channel_id) if channel_id else None,
        "status": "awaiting_user_approval", # Set the correct status
        "proposal_time": now_iso, # Time when it was decided to propose/send preview
        "status_update_time": now_iso # Time status was set
    })
    save_pending_publications(pending)
    logger.info(f"Added mod {mod_id} for user {user_id_str} to pending queue (awaiting user approval) at {now_iso}.")
    return True

def remove_from_pending_by_user_mod(user_id: str, mod_id: int):
    """Removes a specific mod for a specific user from the pending list, regardless of status."""
    pending = load_pending_publications()
    user_id_str = str(user_id)
    original_length = len(pending)
    new_pending = [
        item for item in pending
        if not (item.get("user_id") == user_id_str and item.get("mod_id") == mod_id)
    ]

    if len(new_pending) < original_length:
        save_pending_publications(new_pending)
        logger.info(f"Removed mod {mod_id} for user {user_id_str} from pending queue.")
        return True
    return False

def load_json_file(filename, default_value):
    """Loads data from a JSON file safely."""
    if os.path.exists(filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            logger.error(f"Error reading or decoding JSON from {filename}: {e}")
            return default_value
    return default_value

def save_json_file(filename, data):
    """Saves data to a JSON file safely."""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
    except IOError as e:
        logger.error(f"Failed to save data to {filename}: {e}")
    except TypeError as e:
        logger.error(f"Data type error saving to {filename}: {e} - Data: {data}")


def load_user_mods_status():
    return load_json_file(USER_MODS_STATUS_FILE, {})

def save_user_mods_status(user_id, mod_id):
    status = load_user_mods_status()
    user_id_str = str(user_id)
    if user_id_str not in status:
        status[user_id_str] = []
    if mod_id not in status[user_id_str]:
        status[user_id_str].append(mod_id)
        save_json_file(USER_MODS_STATUS_FILE, status)

# Alias for saving published mods (used in skip action)
def save_published_mod(user_id, mod_id):
    save_user_mods_status(user_id, mod_id)

def load_admin_processed_mods():
    """Loads the set of mod IDs processed by the admin."""
    data = load_json_file(ADMIN_PROCESSED_MODS_FILE, [])
    return set(data) # Convert list to set in memory

def save_admin_processed_mod(mod_id: int):
    """Adds a mod ID to the set of admin-processed mods."""
    processed_mods_set = load_admin_processed_mods()
    if mod_id not in processed_mods_set:
        processed_mods_set.add(mod_id)
        save_json_file(ADMIN_PROCESSED_MODS_FILE, list(processed_mods_set)) # Save as list

def load_pending_publications():
    """Loads the list of items (mods awaiting approval or other states)."""
    data = load_json_file(PENDING_PUBLICATION_FILE, [])
    if isinstance(data, list):
        # Basic validation of structure - ensure essential keys and status
        valid_data = [
            item for item in data
            if isinstance(item, dict) and
               "mod_id" in item and
               "user_id" in item and
               "status" in item # Ensure status key exists
        ]
        if len(valid_data) != len(data):
            logger.warning(f"Some invalid entries found and removed from {PENDING_PUBLICATION_FILE}")
        return valid_data
    else:
        logger.error(f"Invalid format in {PENDING_PUBLICATION_FILE}, expected a list. Returning empty list.")
        return []

def save_pending_publications(pending_list):
    """Saves the list of pending items."""
    save_json_file(PENDING_PUBLICATION_FILE, pending_list)

def add_to_pending_publication(mod_id: int, user_id: str):
    """Adds a mod to the pending publication queue if not already present."""
    pending = load_pending_publications()
    user_id_str = str(user_id) # Ensure user_id is string
    # Check if this specific mod for this specific user is already pending
    if not any(item['mod_id'] == mod_id and item['user_id'] == user_id_str for item in pending):
        # Add channel ID at the time of adding to queue for easier cleanup
        user_data = load_user_channels().get(user_id_str, {})
        channel_id = user_data.get("channel_id") if isinstance(user_data, dict) else None
        pending.append({"mod_id": mod_id, "user_id": user_id_str, "channel_id": channel_id})
        save_pending_publications(pending)
        logger.info(f"Added mod {mod_id} for user {user_id_str} (channel: {channel_id}) to pending publication queue.")
    else:
        logger.info(f"Mod {mod_id} for user {user_id_str} is already in the pending publication queue.")


# --- Channel Watchdog and Queue Cleaning ---

async def check_channel_permissions(bot: telegram.Bot, channel_id: str, user_id: str) -> bool:
    """Checks bot permissions in a channel and notifies the user on failure."""
    if not channel_id:
         logger.warning(f"Permission check skipped for user {user_id}: No channel ID provided.")
         return False
    try:
        # Use bot.get_chat to get channel info (also checks if channel exists)
        chat = await bot.get_chat(channel_id)
        logger.debug(f"Checking permissions for bot {bot.id} in channel {channel_id} ('{chat.title}') for user {user_id}")

        # We need get_chat_member to check the bot's status.
        member = await bot.get_chat_member(channel_id, bot.id)

        # Check if the bot is an administrator and has the specific permission
        if isinstance(member, (ChatMemberAdministrator, ChatMemberOwner)):
             if member.can_post_messages:
                 logger.debug(f"Permissions check OK for channel {channel_id} (User: {user_id})")
                 return True
             else:
                 logger.warning(f"Bot is admin but cannot post messages in channel {channel_id} (User: {user_id})")
                 await bot.send_message(user_id, f"❌ البوت لم يعد لديه صلاحية النشر في القناة (`{channel_id}`). يرجى التحقق من صلاحيات المشرفين وتفعيل 'نشر الرسائل'.")
                 return False
        else:
             logger.warning(f"Bot is not an admin or member in channel {channel_id} (User: {user_id}). Member status: {member.status}")
             await bot.send_message(user_id, f"❌ البوت ليس مشرفًا في القناة (`{channel_id}`) أو تم طرده. يرجى إضافته كمشرف ومنحه صلاحية 'نشر الرسائل'.")
             return False

    except BadRequest as e:
        # Handle specific errors like chat not found or bot kicked/banned
        error_message = str(e).lower()
        user_notification = f"⚠️ حدث خطأ غير متوقع أثناء التحقق من صلاحيات القناة (`{channel_id}`)." # Default
        log_level = logging.ERROR

        if "chat not found" in error_message or "user not found" in error_message or "peer_id_invalid" in error_message:
             log_level = logging.WARNING
             user_notification = f"⚠️ تعذر الوصول إلى القناة (`{channel_id}`). قد تكون حُذفت أو تم تغيير معرفها، أو تم طرد البوت منها. يرجى التحقق وإعادة ربط القناة إذا لزم الأمر."
        elif "bot was kicked" in error_message or "user_is_bot" in error_message: # Sometimes 'kicked' isn't explicit
             log_level = logging.WARNING
             user_notification = f"❌ تم طرد البوت من القناة (`{channel_id}`). يرجى إعادة إضافته كمشرف."
        elif "not enough rights" in error_message:
             log_level = logging.WARNING
             user_notification = f"❌ البوت ليس لديه الصلاحيات الكافية للتحقق من القناة (`{channel_id}`). تأكد من أنه عضو على الأقل."
        else:
             user_notification = f"⚠️ حدث خطأ ({e.message}) أثناء التحقق من صلاحيات القناة (`{channel_id}`)."

        logger.log(log_level, f"BadRequest checking permissions for channel {channel_id} (User: {user_id}): {e}")
        try:
             await bot.send_message(user_id, user_notification)
        except TelegramError as notify_err:
             logger.error(f"Failed to notify user {user_id} about channel check error: {notify_err}")
        return False
    except TelegramError as e: # Catch other potential Telegram API errors (timeouts, etc.)
        logger.error(f"TelegramError checking permissions for channel {channel_id} (User: {user_id}): {e}")
        # Don't notify user on general network errors unless persistent
        # await bot.send_message(user_id, f"⚠️ واجه البوت مشكلة مؤقتة أثناء الاتصال بالقناة (`{channel_id}`). سيحاول مرة أخرى لاحقاً.")
        return False # Treat temporary errors as failure for now
    except Exception as e:
        # Catch any other unexpected errors
        logger.error(f"Unexpected error checking permissions for channel {channel_id} (User: {user_id}): {e}", exc_info=True)
        try:
            await bot.send_message(user_id, f"⚠️ حدث خطأ عام غير معروف أثناء التحقق من القناة (`{channel_id}`).")
        except TelegramError as notify_err:
            logger.error(f"Failed to notify user {user_id} about unexpected channel check error: {notify_err}")
        return False

# Function to clean the pending queue
def clean_pending_queue(user_id, old_channel_id=None, new_channel_id=None):
    """Removes or updates items in the pending queue based on channel changes or user deletion."""
    pending = load_pending_publications()
    original_count = len(pending)
    updated_pending = []
    removed_count = 0
    updated_count = 0
    user_id_str = str(user_id) # Ensure comparison with string IDs

    logger.info(f"Cleaning pending queue for user {user_id_str}. Old Channel: {old_channel_id}, New Channel: {new_channel_id}")

    for item in pending:
        # Check if the item belongs to the affected user
        item_user_id = item.get("user_id")
        item_channel_id = item.get("channel_id") # Channel ID stored when item was added

        if item_user_id == user_id_str:
            # Case 1: User deleted entirely (indicated by clean_pending_queue(user_id) call)
            if old_channel_id is None and new_channel_id is None:
                 logger.info(f"Removing pending item (Mod {item.get('mod_id')}, Status: {item.get('status')}) for deleted user {user_id_str}.")
                 removed_count += 1
                 continue # Skip adding this item

            # Case 2: Channel was changed or deleted (old_channel_id is provided)
            # We check against the channel_id *stored in the item*
            elif old_channel_id and item_channel_id == old_channel_id:
                # Subcase 2a: Channel ID was changed (new_channel_id provided)
                if new_channel_id:
                    logger.info(f"Updating channel ID for pending item (Mod {item.get('mod_id')}) for user {user_id_str}. Old Item Channel: {item_channel_id}, New User Channel: {new_channel_id}")
                    item["channel_id"] = new_channel_id # Update the channel ID in the item
                    updated_pending.append(item)
                    updated_count += 1
                # Subcase 2b: Channel was deleted (new_channel_id is None)
                else:
                    logger.info(f"Removing pending item (Mod {item.get('mod_id')}) for user {user_id_str} (old channel {old_channel_id} associated with this item was deleted/unlinked).")
                    removed_count += 1
                    continue # Skip adding this item
            # Case 3: Item belongs to the user, but the channel associated with *this item* wasn't the one changed/deleted. Keep it.
            # Or if the channel wasn't changed at all (e.g., settings update without channel change).
            else:
                 logger.debug(f"Keeping pending item (Mod {item.get('mod_id')}, Status: {item.get('status')}) for user {user_id_str} (item channel {item_channel_id} != changed old channel {old_channel_id}).")
                 updated_pending.append(item)

        # Item belongs to a different user, keep it
        else:
            updated_pending.append(item)

    # Save the modified list if changes were made
    if removed_count > 0 or updated_count > 0:
        logger.info(f"Pending queue cleaned for user {user_id_str}. Removed: {removed_count}, Updated: {updated_count}. Original size: {original_count}, New size: {len(updated_pending)}")
        save_pending_publications(updated_pending)
    else:
        logger.debug(f"No changes needed in pending queue for user {user_id_str} regarding channel update ({old_channel_id} -> {new_channel_id}).")

# --- File Initialization ---

# <<< --- START: Admin Settings Helpers --- >>>
def load_admin_settings():
    """Loads admin settings from file, returning defaults if not found or invalid."""
    defaults = {"admin_preview_required": True} # Default: Admin must approve
    settings = load_json_file(ADMIN_SETTINGS_FILE, defaults)
    if not isinstance(settings, dict):
        logger.warning(f"Invalid format in {ADMIN_SETTINGS_FILE}, using defaults.")
        return defaults
    # Ensure the required key exists, defaulting to True if missing
    settings.setdefault("admin_preview_required", defaults["admin_preview_required"])
    return settings

def save_admin_settings(settings_dict):
    """Saves the admin settings dictionary to the file."""
    if isinstance(settings_dict, dict) and "admin_preview_required" in settings_dict:
        save_json_file(ADMIN_SETTINGS_FILE, settings_dict)
    else:
        logger.error(f"Attempted to save invalid admin settings: {settings_dict}")

def is_admin_preview_required():
    """Checks if the admin preview step is currently required."""
    settings = load_admin_settings()
    # Returns the value, defaulting to True if key somehow missing after loading
    return settings.get("admin_preview_required", True)

# <<< --- END: Admin Settings Helpers --- >>>

def initialize_files():
    """Initializes necessary data files if they don't exist."""
    required_files = {
        MODS_FILE: [],
        USER_CHANNELS_FILE: {},
        ALL_USERS_FILE: {},  # ملف جديد لحفظ جميع المستخدمين
        ADMIN_PROCESSED_MODS_FILE: [],
        PENDING_PUBLICATION_FILE: [],
        USER_FEEDBACK_FILE: {},
        USER_MODS_STATUS_FILE: {},
        USER_BLOCKED_MODS_FILE: {},
        ADMIN_SETTINGS_FILE: {"admin_preview_required": True} # <-- أضف الملف الجديد وإعداداته الافتراضية
    }
    os.makedirs(os.path.dirname(MODS_FILE) or '.', exist_ok=True) # Ensure directory exists if specified

    for file_path, default_content in required_files.items():
        if not os.path.exists(file_path):
            try:
                # Use save_json_file to handle encoding and errors
                save_json_file(file_path, default_content)
                logger.info(f"تم إنشاء الملف {file_path}")
            except Exception as e:
                logger.error(f"Failed to create file {file_path}: {e}")


# --- User Interface Handlers ---

async def handle_language_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handles language selection and shows channel linking prompt with Continue/Skip options.
    """
    query = update.callback_query
    await query.answer()

    lang = query.data.split('_')[1]
    user_id = query.from_user.id
    user_id_str = str(user_id)

    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})

    if isinstance(user_data, dict):
        user_data['lang'] = lang
    else:
        user_data = {
            "channel_id": user_data if isinstance(user_data, str) else None,
            "lang": lang,
            "publish_interval": 60,
            "active": True,
            "preview_enabled": False,
            "last_publish_time": None,
            "mod_categories": ['addons', 'shaders', 'texture_packs', 'seeds', 'maps'], # Default: all categories
            "mc_versions": ['1.21+', '1.20+', '1.19+', '1.18+'], # Default: all versions
            "message_format": "classic", # Default: classic format
            "channel_lang": None # Default: use user's main language (will be set during setup)
        }

    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # حفظ المستخدم في ملف جميع المستخدمين
    user_info = {
        "lang": lang,
        "username": query.from_user.username or "",
        "full_name": query.from_user.full_name or ""
    }
    save_user_to_all_users(user_id, user_info)

    logger.info(f"User {user_id_str} selected language '{lang}'. Showing channel linking prompt.")

    # --- رسالة طلب ربط القناة مع خيارين ---
    channel_linking_texts = {
        "ar": {
            "title": "✅ تم تحديد اللغة إلى العربية بنجاح!",
            "message": "🔗 <b>ربط القناة</b>\n\nلاستخدام البوت بشكل كامل، يُنصح بربط قناتك لتلقي المودات تلقائياً.\n\n📌 يمكنك ربط القناة الآن أو تخطي هذه الخطوة والعودة إليها لاحقاً من القائمة الرئيسية.",
            "continue": "📎 متابعة - ربط القناة",
            "skip": "⏭️ تخطي - الذهاب للقائمة الرئيسية"
        },
        "en": {
            "title": "✅ Language set to English successfully!",
            "message": "🔗 <b>Channel Linking</b>\n\nTo use the bot fully, it's recommended to link your channel to receive mods automatically.\n\n📌 You can link your channel now or skip this step and return to it later from the main menu.",
            "continue": "📎 Continue - Link Channel",
            "skip": "⏭️ Skip - Go to Main Menu"
        }
    }

    text = channel_linking_texts.get(lang, channel_linking_texts['ar'])

    # بناء الرسالة الكاملة
    full_message = f"{text['title']}\n\n{text['message']}"

    # بناء الأزرار
    keyboard = [
        [InlineKeyboardButton(text["continue"], callback_data="channel_link_continue")],
        [InlineKeyboardButton(text["skip"], callback_data="channel_link_skip")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # تحديث الرسالة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, full_message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def handle_channel_link_continue(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles the 'Continue - Link Channel' button press."""
    query = update.callback_query
    await query.answer()

    # استدعاء دالة طلب إضافة القناة مباشرة
    await prompt_add_change_channel(update, context)


async def handle_channel_link_skip(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles the 'Skip - Go to Main Menu' button press."""
    query = update.callback_query
    await query.answer()

    # الذهاب مباشرة للقائمة الرئيسية
    await show_main_menu(update, context)


async def send_single_user_preview(context: ContextTypes.DEFAULT_TYPE, user_id: str, mod: dict, lang: str): # Pass user_id as string
    """Sends a preview to a single user matching the final post format, including Approve/Reject/Block buttons."""
    user_id_str = str(user_id) # Ensure it's a string

    # --- <<<< التعديل يبدأ هنا >>>> ---

    # 1. احصل على الكابشن النهائي المطابق للمنشور من الدالة المساعدة
    #    (نتجاهل reply_markup الخاص بزر التحميل)
    post_content = _build_mod_post_content(mod, lang)
    caption = post_content["caption"] # هذا هو الكابشن الذي سيظهر في القناة

    # 2. قم ببناء أزرار المعاينة الخاصة بالمستخدم (موافقة/رفض/حظر)
    texts = {
        "ar": {"buttons": ["✅ موافقة ونشر", "❌ رفض", "🚫 حظر نهائي"]},
        "en": {"buttons": ["✅ Approve & Publish", "❌ Reject", "🚫 Block Permanently"]}
    }
    button_texts = texts.get(lang, texts['ar'])["buttons"] # Default to Arabic if lang not found

    keyboard = [
        [
            InlineKeyboardButton(button_texts[0], callback_data=f"user_approve_{mod['id']}"),
            InlineKeyboardButton(button_texts[1], callback_data=f"user_reject_{mod['id']}")
        ],
        [
            InlineKeyboardButton(button_texts[2], callback_data=f"user_block_{mod['id']}")
        ]
    ]
    preview_reply_markup = InlineKeyboardMarkup(keyboard) # هذه هي أزرار المعاينة

    # --- <<<< التعديل ينتهي هنا >>>> ---

    try:
        image_url = mod.get("image_url")
        if image_url:
            await context.bot.send_photo(
                chat_id=user_id_str,
                photo=image_url,
                caption=caption,                # استخدام الكابشن المطابق للمنشور
                reply_markup=preview_reply_markup, # استخدام أزرار المعاينة
                parse_mode="HTML"
            )
        else:
            await context.bot.send_message(
                chat_id=user_id_str,
                text=caption,                 # استخدام الكابشن المطابق للمنشور
                reply_markup=preview_reply_markup,  # استخدام أزرار المعاينة
                parse_mode="HTML",
                disable_web_page_preview=True # مهم لكي لا تظهر معاينة رابط كيفية التركيب
            )
        logger.info(f"Sent enhanced preview for mod {mod.get('id', 'N/A')} (lang: {lang}) to user {user_id_str}")
    except BadRequest as e:
         logger.error(f"Failed to send enhanced preview to user {user_id_str} (BadRequest: {e}). Maybe blocked or invalid ID?")
         # Consider removing from pending if sending fails?
         # remove_from_pending_by_user_mod(user_id_str, mod.get('id'))
    except TelegramError as e:
        logger.error(f"Failed to send enhanced preview to user {user_id_str} (TelegramError: {e})")
        # Consider removing from pending?
    except Exception as e:
        logger.error(f"فشل إرسال معاينة محسنة للمستخدم {user_id_str}: {str(e)}")
        # Consider removing from pending?
def load_user_feedback():
    """تحميل تقييمات المستخدمين من الملف"""
    return load_json_file(USER_FEEDBACK_FILE, {})

def save_user_feedback(user_id, mod_id, is_approved):
    """حفظ تقييم المستخدم في الملف"""
    feedback = load_user_feedback()
    key = f"{user_id}_{mod_id}"
    feedback[key] = is_approved
    save_json_file(USER_FEEDBACK_FILE, feedback)

# --- دوال إدارة ملف جميع المستخدمين ---

def load_all_users():
    """تحميل قائمة جميع المستخدمين"""
    return load_json_file(ALL_USERS_FILE, {})

def save_user_to_all_users(user_id, user_data):
    """حفظ بيانات المستخدم في ملف جميع المستخدمين"""
    all_users = load_all_users()
    user_id_str = str(user_id)

    # إذا كان المستخدم موجود، نحديث بياناته
    if user_id_str in all_users:
        all_users[user_id_str].update(user_data)
    else:
        # إضافة مستخدم جديد
        all_users[user_id_str] = {
            "first_seen": datetime.now().isoformat(),
            "last_activity": datetime.now().isoformat(),
            "lang": user_data.get("lang", "ar"),
            "username": user_data.get("username", ""),
            "full_name": user_data.get("full_name", ""),
            **user_data
        }

    # تحديث آخر نشاط
    all_users[user_id_str]["last_activity"] = datetime.now().isoformat()

    save_json_file(ALL_USERS_FILE, all_users)
    logger.info(f"User {user_id_str} saved to all_users file")

def update_user_activity(user_id):
    """تحديث آخر نشاط للمستخدم"""
    all_users = load_all_users()
    user_id_str = str(user_id)

    if user_id_str in all_users:
        all_users[user_id_str]["last_activity"] = datetime.now().isoformat()
        save_json_file(ALL_USERS_FILE, all_users)

async def update_ui_response(query, text, is_photo=False, reply_markup=None, parse_mode=None):
    """تحديث الرسالة في الواجهة سواء كانت صورة أو نص"""
    # --- إضافة: التحقق من وجود query و query.message ---
    if not query or not query.message:
        logger.warning("update_ui_response called with invalid query or query.message is None.")
        if query:
            try:
                # محاولة الرد على الاستعلام حتى لو لم تكن الرسالة موجودة لمسح حالة التحميل
                await query.answer("Error: Original message not found.", show_alert=True)
            except Exception:
                pass # تجاهل الخطأ إذا فشل الرد أيضاً
        return
    # --- نهاية الإضافة ---

    try:
        if is_photo:
            current_caption = query.message.caption
            current_markup = query.message.reply_markup
            # التحقق مما إذا كان التعديل ضرورياً
            if current_caption != text or current_markup != reply_markup:
                await query.edit_message_caption(
                    caption=text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode # <-- استخدام المتغير parse_mode
                )
                logger.debug(f"Edited message caption (Query ID: {query.id})")
            else:
                logger.debug(f"Caption and markup unchanged, skipping edit (Query ID: {query.id}).")
                # الرد بصمت إذا لم يتغير شيء لإزالة حالة التحميل
                await query.answer()
        else:
            current_text = query.message.text
            current_markup = query.message.reply_markup
            # التحقق مما إذا كان التعديل ضرورياً
            if current_text != text or current_markup != reply_markup:
                await query.edit_message_text(
                    text=text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode # <-- استخدام المتغير parse_mode
                )
                logger.debug(f"Edited message text (Query ID: {query.id})")
            else:
                logger.debug(f"Text and markup unchanged, skipping edit (Query ID: {query.id}).")
                # الرد بصمت إذا لم يتغير شيء لإزالة حالة التحميل
                await query.answer()

    except BadRequest as e:
        error_message = str(e)
        if "Message is not modified" in error_message:
            logger.debug(f"Message not modified, skipping update. Query ID: {query.id}")
            # الرد على الاستعلام بصمت لأن الحالة صحيحة بالفعل
            await query.answer()
        elif "message to edit not found" in error_message.lower():
             logger.warning(f"Failed to edit message: Message not found (likely deleted). Query ID: {query.id}")
             # تنبيه المستخدم بأن الإجراء لم يكتمل على الرسالة
             await query.answer("Error: Original message not found.", show_alert=True)
        else:
            # التعامل مع أخطاء BadRequest الأخرى (مثل وضع تحليل غير صالح، أخطاء الكيانات)
            logger.error(f"Failed to update UI (BadRequest): {error_message} - Query Data: {query.data}", exc_info=True)
            # تنبيه المستخدم بالخطأ
            await query.answer("Error processing request (BadRequest).", show_alert=True)
    except TelegramError as e:
         # التعامل مع أخطاء Telegram API الأخرى (مثل مشاكل الشبكة، انتظار الفيضان)
         logger.error(f"Failed to update UI (TelegramError): {str(e)} - Query Data: {query.data}", exc_info=True)
         # تنبيه المستخدم بخطأ API عام
         await query.answer("Telegram API error. Please try again.", show_alert=True)
    except Exception as e:
        # التقاط أي استثناءات أخرى غير متوقعة
        logger.error(f"Failed to update UI (Unexpected): {str(e)} - Query Data: {query.data}", exc_info=True)
        # تنبيه المستخدم بخطأ داخلي غير متوقع
        await query.answer("An unexpected error occurred.", show_alert=True)

# --- Admin Panel ---

async def admin_panel(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض واجهة إدارة المسؤول"""
    user = update.effective_user
    if str(user.id) != YOUR_CHAT_ID: # Check against configured admin ID
        if update.message:
            await update.message.reply_text("❌ ليس لديك صلاحية الوصول!")
        elif update.callback_query:
            await update.callback_query.answer("❌ ليس لديك صلاحية الوصول!", show_alert=True)
        logger.warning(f"Unauthorized access attempt to admin panel by user {user.id} (@{user.username})")
        return

    # <<< --- START: Admin Preview Toggle Button Logic --- >>>
    admin_preview_on = is_admin_preview_required()
    preview_button_text = "إيقاف معاينة المسؤول ❌" if admin_preview_on else "تفعيل معاينة المسؤول ✅"
    preview_button_callback = "admin_toggle_preview"
    admin_preview_toggle_button = InlineKeyboardButton(preview_button_text, callback_data=preview_button_callback)
    # <<< --- END: Admin Preview Toggle Button Logic --- >>>

    keyboard = [
        [InlineKeyboardButton("📊 إحصائيات البوت", callback_data="admin_stats")],
        [InlineKeyboardButton("👥 إدارة المستخدمين", callback_data="admin_list_users")],
        [admin_preview_toggle_button],
        [InlineKeyboardButton("🔄 إعادة نشر مود", callback_data="admin_republish_list")],
        [InlineKeyboardButton("🗑 حذف مود", callback_data="admin_delete_mod_list")],
        [InlineKeyboardButton("📢 بث رسالة للمستخدمين", callback_data="admin_broadcast_menu")],
        [InlineKeyboardButton("🔄 إعادة تشغيل النشر", callback_data="admin_restart_publishing")],
        # --- <<< زر تفعيل النشر الجديد >>> ---
        [InlineKeyboardButton("✅ تفعيل النشر للكل (المتوقف)", callback_data="admin_force_enable_all_publish")], # <-- الزر المضاف هنا
        # --- <<< نهاية الإضافة >>> ---
        [InlineKeyboardButton("🔙 العودة (للمستخدم)", callback_data="main_menu")] # أو العودة للقائمة الرئيسية للبوت إن وجد
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    text = "🛠 لوحة تحكم المسؤول:"

    # --- تحديث الواجهة أو إرسالها ---
    if update.callback_query:
        query = update.callback_query
        is_photo = bool(query.message and query.message.photo)
        try:
            # Attempt to edit the message
            await update_ui_response(query, text, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML") # <-- أضف parse_mode
        except BadRequest as e:
            if "Message is not modified" in str(e):
                 logger.debug("Admin panel message not modified.")
                 await query.answer() # Answer silently
            elif "message to edit not found" in str(e).lower():
                 logger.warning("Admin panel message deleted, sending new one.")
                 await query.message.reply_text(text, reply_markup=reply_markup, parse_mode="HTML") # Send if edit fails
            else:
                 logger.error(f"Error updating admin panel: {e}")
                 await query.answer("Error updating panel.", show_alert=True)
        except Exception as e:
             logger.error(f"Unexpected error updating admin panel: {e}", exc_info=True)
             await query.answer("Unexpected error.", show_alert=True)

    elif update.message:
        # If called by command, just send the message
        await update.message.reply_text(text, reply_markup=reply_markup, parse_mode="HTML") # Assuming text might have HTML
    # --- نهاية التعديل ---

async def notify_users_of_activation(context: ContextTypes.DEFAULT_TYPE, user_ids: list[str]):
    """Sends a notification message to users whose publishing was reactivated by the admin."""
    logger.info(f"Notifying {len(user_ids)} users about forced reactivation.")
    success_count = 0
    failure_count = 0
    for user_id_str in user_ids:
        lang = get_user_lang(user_id_str) # Get user's language
        texts = {
            "ar": "ℹ️ تم إعادة تفعيل ميزة النشر التلقائي في قناتك بواسطة المسؤول.",
            "en": "ℹ️ The automatic publishing feature for your channel has been reactivated by the admin."
        }
        message_text = texts.get(lang, texts['ar'])
        try:
            await context.bot.send_message(chat_id=user_id_str, text=message_text)
            success_count += 1
            await asyncio.sleep(0.1) # Small delay to avoid rate limits
        except Forbidden:
            logger.warning(f"Failed to notify user {user_id_str} about reactivation: Forbidden (Bot blocked).")
            failure_count += 1
        except BadRequest as e:
            logger.warning(f"Failed to notify user {user_id_str} about reactivation: BadRequest ({e}).")
            failure_count += 1
        except Exception as e:
            logger.error(f"Unexpected error notifying user {user_id_str} about reactivation: {e}")
            failure_count += 1
    logger.info(f"Reactivation notification results: Success={success_count}, Failed={failure_count}")


async def admin_force_enable_publish_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles the admin request to force-enable publishing for all inactive user channels."""
    query = update.callback_query
    if str(query.from_user.id) != YOUR_CHAT_ID:
        await query.answer("❌ ليس لديك صلاحية الوصول!", show_alert=True)
        return

    await query.answer("⏳ جارٍ تفعيل النشر للقنوات المتوقفة...") # Acknowledge with processing message

    user_channels = load_user_channels()
    updated_count = 0
    users_to_notify = [] # List to store IDs of users who were reactivated

    try:
        # Iterate through a copy of the items to allow modification
        for user_id_str, user_data in list(user_channels.items()):
            # Check if it's a dictionary and if 'active' is explicitly False
            if isinstance(user_data, dict) and user_data.get("active") is False:
                user_data["active"] = True # Set to active
                updated_count += 1
                users_to_notify.append(user_id_str)
                logger.info(f"Admin force-enabled publishing for user {user_id_str}.")

        # Save changes only if any user was updated
        if updated_count > 0:
            save_json_file(USER_CHANNELS_FILE, user_channels)
            # Send confirmation to admin
            confirmation_text = f"✅ تم تفعيل النشر بنجاح لـ {updated_count} مستخدم كانوا قد أوقفوا النشر."
            # Optionally, start the notification task in the background
            if users_to_notify:
                 asyncio.create_task(notify_users_of_activation(context, users_to_notify))
        else:
            confirmation_text = "ℹ️ لم يتم العثور على مستخدمين لديهم النشر متوقف حالياً."

        # Update the admin panel message
        is_photo = bool(query.message and query.message.photo)
        admin_panel_text = query.message.caption if is_photo else query.message.text
        # Ensure the original text exists before appending
        if admin_panel_text:
             updated_admin_text = admin_panel_text + f"\n\n---\n{confirmation_text}"
        else: # Fallback if original text is missing
             updated_admin_text = confirmation_text

        # Use the original reply_markup from the admin panel for consistency
        # We can fetch it again or assume it's the same structure
        # Let's rebuild it for simplicity here, ensure it matches admin_panel
        admin_preview_on = is_admin_preview_required()
        preview_button_text = "إيقاف معاينة المسؤول ❌" if admin_preview_on else "تفعيل معاينة المسؤول ✅"
        keyboard = [
            [InlineKeyboardButton("📊 إحصائيات البوت", callback_data="admin_stats")],
            [InlineKeyboardButton("👥 إدارة المستخدمين", callback_data="admin_list_users")],
            [InlineKeyboardButton(preview_button_text, callback_data="admin_toggle_preview")],
            [InlineKeyboardButton("🔄 إعادة نشر مود", callback_data="admin_republish_list")],
            [InlineKeyboardButton("🗑 حذف مود", callback_data="admin_delete_mod_list")],
            [InlineKeyboardButton("📢 بث رسالة للمستخدمين", callback_data="admin_broadcast_menu")],
            [InlineKeyboardButton("🔄 إعادة تشغيل النشر", callback_data="admin_restart_publishing")],
            [InlineKeyboardButton("✅ تفعيل النشر للكل (المتوقف)", callback_data="admin_force_enable_all_publish")],
            [InlineKeyboardButton("🔙 العودة (للمستخدم)", callback_data="main_menu")]
        ]
        updated_markup = InlineKeyboardMarkup(keyboard)


        await update_ui_response(query, updated_admin_text, is_photo=is_photo, reply_markup=updated_markup, parse_mode="HTML")

    except Exception as e:
        logger.error(f"Error in admin_force_enable_publish_handler: {e}", exc_info=True)
        await query.edit_message_text("❌ حدث خطأ أثناء محاولة تفعيل النشر للكل.") # Edit message on error
        await send_notification(f"🚨 خطأ فادح في وظيفة تفعيل النشر للكل: <pre>{html.escape(str(e))}</pre>", context)

async def admin_broadcast_menu_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Displays the broadcast target selection menu."""
    query = update.callback_query
    if str(query.from_user.id) != YOUR_CHAT_ID:
        await query.answer("❌ ليس لديك صلاحية الوصول!", show_alert=True)
        return
    await query.answer()

    # Clear previous broadcast state just in case
    context.user_data.pop('awaiting_broadcast_target', None)
    context.user_data.pop('awaiting_broadcast_user_id', None)
    context.user_data.pop('awaiting_broadcast_message', None)
    context.user_data.pop('broadcast_target_type', None)
    context.user_data.pop('broadcast_specific_user_id', None)


    keyboard = [
        [
            InlineKeyboardButton("📬 للكل", callback_data="broadcast_target_all"),
            InlineKeyboardButton("🇸🇦 للعربية فقط", callback_data="broadcast_target_ar"),
            InlineKeyboardButton("🇬🇧 للإنجليزية فقط", callback_data="broadcast_target_en"),
        ],
        [
            InlineKeyboardButton("👤 لمستخدم محدد", callback_data="broadcast_target_specific"),
        ],
        [
            InlineKeyboardButton("🔙 إلغاء", callback_data="admin_panel") # Back to admin panel
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    text = "📢 <b>بث رسالة:</b>\n\nاختر الجمهور المستهدف لإرسال الرسالة إليه:"

    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, text, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML") # <-- أضف parse_mode
    # Set state indicating we are now waiting for a target selection (or cancellation)
    context.user_data['awaiting_broadcast_target'] = True

async def admin_broadcast_target_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles the selection of the broadcast target audience."""
    query = update.callback_query
    if str(query.from_user.id) != YOUR_CHAT_ID:
        await query.answer("❌ ليس لديك صلاحية الوصول!", show_alert=True)
        return
    await query.answer()

    target_type = query.data.split("_")[-1] # all, ar, en, specific

    context.user_data['awaiting_broadcast_target'] = False # No longer waiting for target selection
    context.user_data['broadcast_target_type'] = target_type

    text = ""
    next_state_key = ""
    prompt_message = ""

    if target_type == "specific":
        prompt_message = "👤 يرجى الآن إرسال <b>معرف المستخدم (User ID)</b> الرقمي الذي تريد إرسال الرسالة إليه."
        next_state_key = 'awaiting_broadcast_user_id'
    elif target_type in ["all", "ar", "en"]:
        target_map = {"all": "الكل", "ar": "مستخدمي العربية", "en": "مستخدمي الإنجليزية"}
        prompt_message = (f"✅ تم تحديد الجمهور: <b>{target_map.get(target_type)}</b>.\n\n"
                          f"✍️ يرجى الآن إرسال الرسالة التي تريد بثها (يمكن أن تكون نص، صورة مع تعليق، إلخ)."
                          f"\n\n<i>سيتم نسخ الرسالة التالية التي ترسلها وإرسالها للمستخدمين المستهدفين.</i>")
        next_state_key = 'awaiting_broadcast_message'
    else:
        logger.warning(f"Invalid broadcast target type: {target_type}")
        await update_ui_response(query, "❌ خيار غير صالح.", is_photo=bool(query.message.photo))
        # Go back to menu?
        await admin_panel(update, context)
        return

    # Set the next expected state
    context.user_data[next_state_key] = True

    is_photo = bool(query.message and query.message.photo)
    # Include a cancel button in the prompt message
    cancel_button = InlineKeyboardButton("❌ إلغاء البث", callback_data="broadcast_cancel")
    reply_markup = InlineKeyboardMarkup([[cancel_button]])
    await update_ui_response(query, prompt_message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")

async def execute_broadcast(
    context: ContextTypes.DEFAULT_TYPE,
    message_to_copy: telegram.Message, # The admin's message object
    target_type: str, # 'all', 'ar', 'en', 'specific'
    specific_user_id: str = None # Required if target_type is 'specific'
    ) -> tuple[int, int]: # Returns (success_count, failure_count)
    """Sends the admin's message to the target audience."""
    admin_id = message_to_copy.from_user.id # Get admin ID from the message

    logger.info(f"Executing broadcast: Target='{target_type}', SpecificID='{specific_user_id}', Initiated by Admin='{admin_id}'")

    user_channels = load_user_channels()
    target_user_ids = []

    # 1. Filter users based on target_type - استخدام جميع المستخدمين بدلاً من المستخدمين الذين لديهم قنوات فقط
    all_users = load_all_users()  # تحميل جميع المستخدمين

    if target_type == "all":
        # جميع المستخدمين من ملف all_users + المستخدمين الذين لديهم قنوات
        target_user_ids = list(set(list(all_users.keys()) + list(user_channels.keys())))
    elif target_type == "ar":
        # المستخدمين العرب من كلا الملفين
        target_user_ids = []
        # من ملف جميع المستخدمين
        target_user_ids.extend([uid for uid, data in all_users.items() if isinstance(data, dict) and data.get('lang', 'ar') == 'ar'])
        # من ملف المستخدمين الذين لديهم قنوات
        target_user_ids.extend([uid for uid, data in user_channels.items() if isinstance(data, dict) and data.get('lang', 'ar') == 'ar'])
        target_user_ids = list(set(target_user_ids))  # إزالة التكرار
    elif target_type == "en":
        # المستخدمين الإنجليز من كلا الملفين
        target_user_ids = []
        # من ملف جميع المستخدمين
        target_user_ids.extend([uid for uid, data in all_users.items() if isinstance(data, dict) and data.get('lang') == 'en'])
        # من ملف المستخدمين الذين لديهم قنوات
        target_user_ids.extend([uid for uid, data in user_channels.items() if isinstance(data, dict) and data.get('lang') == 'en'])
        target_user_ids = list(set(target_user_ids))  # إزالة التكرار
    elif target_type == "specific" and specific_user_id:
        # Ensure the specific user exists in our records (optional but good practice)
        if specific_user_id in user_channels:
            target_user_ids = [specific_user_id]
        else:
            logger.warning(f"Broadcast target 'specific' user {specific_user_id} not found in user_channels.")
            return 0, 1 # 0 success, 1 failure (the target wasn't found)
    else:
        logger.error(f"Invalid target_type '{target_type}' or missing specific_user_id for broadcast.")
        return 0, 0 # Indicate invalid target

    if not target_user_ids:
        logger.warning(f"No users found matching broadcast target '{target_type}'.")
        return 0, 0

    logger.info(f"Broadcasting message to {len(target_user_ids)} users.")
    success_count = 0
    failure_count = 0

    # 2. Iterate and send using copy_message
    for user_id_str in target_user_ids:
        # Skip sending to the admin themselves if they are in the list (e.g., target_all)
        if user_id_str == str(admin_id):
            logger.debug(f"Skipping broadcast to admin ({admin_id}) themselves.")
            continue

        try:
            await context.bot.copy_message(
                chat_id=user_id_str,
                from_chat_id=message_to_copy.chat_id,
                message_id=message_to_copy.message_id
            )
            success_count += 1
            logger.debug(f"Broadcast message successfully sent to user {user_id_str}")
        except Forbidden:
            logger.warning(f"Failed to broadcast to user {user_id_str}: Forbidden (Bot blocked or kicked).")
            failure_count += 1
        except BadRequest as e:
            # Handle common BadRequest errors during broadcast
            error_msg = str(e).lower()
            if "chat not found" in error_msg or "user not found" in error_msg or "peer id invalid" in error_msg:
                logger.warning(f"Failed to broadcast to user {user_id_str}: BadRequest (Chat/User not found).")
            elif "user is deactivated" in error_msg:
                 logger.warning(f"Failed to broadcast to user {user_id_str}: BadRequest (User deactivated).")
            else:
                 logger.error(f"Failed to broadcast to user {user_id_str}: BadRequest ({e}).")
            failure_count += 1
        except TelegramError as e:
            logger.error(f"Failed to broadcast to user {user_id_str}: TelegramError ({e}).")
            failure_count += 1
        except Exception as e:
            logger.error(f"Unexpected error broadcasting to user {user_id_str}: {e}", exc_info=True)
            failure_count += 1

        # Add a small delay to avoid hitting rate limits
        await asyncio.sleep(0.1) # Adjust delay as needed (e.g., 0.05 for faster, 0.2 for slower)

    logger.info(f"Broadcast finished. Success: {success_count}, Failed: {failure_count}")
    return success_count, failure_count

async def admin_handle_broadcast_input(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles admin input when waiting for broadcast user ID or message."""
    if not update.message or str(update.effective_user.id) != YOUR_CHAT_ID:
        # Ignore messages not from the admin or non-message updates
        return

    user_data = context.user_data
    admin_id = str(update.effective_user.id)

    # --- الحالة 1: انتظار معرف المستخدم ---
    if user_data.get('awaiting_broadcast_user_id'):
        user_id_input = update.message.text.strip()
        if user_id_input.isdigit():
            target_user_id = user_id_input
            logger.info(f"Admin {admin_id} provided specific user ID for broadcast: {target_user_id}")

            # Check if user exists (optional but good)
            all_users = load_user_channels()
            if target_user_id not in all_users:
                 await update.message.reply_text(
                    f"⚠️ المستخدم <code>{target_user_id}</code> غير موجود في سجلات البوت. يرجى إدخال ID صحيح أو إلغاء العملية.",
                    parse_mode="HTML",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("❌ إلغاء البث", callback_data="broadcast_cancel")]])
                 )
                 # Keep waiting for a valid ID
                 return

            # Valid ID received, clear waiting state for ID, set waiting state for message
            user_data.pop('awaiting_broadcast_user_id', None)
            user_data['broadcast_specific_user_id'] = target_user_id
            user_data['awaiting_broadcast_message'] = True

            prompt_message = (f"✅ تم تحديد المستخدم: <code>{target_user_id}</code>.\n\n"
                              f"✍️ يرجى الآن إرسال الرسالة التي تريد بثها له."
                              f"\n\n<i>سيتم نسخ الرسالة التالية التي ترسلها.</i>")
            await update.message.reply_text(
                prompt_message,
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("❌ إلغاء البث", callback_data="broadcast_cancel")]])
            )
        else:
            # Invalid User ID format
            await update.message.reply_text(
                "❌ معرف المستخدم يجب أن يكون رقمياً. يرجى المحاولة مرة أخرى أو الإلغاء.",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("❌ إلغاء البث", callback_data="broadcast_cancel")]])
                )
            # Keep waiting for a valid ID
        return # End processing for user ID input

    # --- الحالة 2: انتظار رسالة البث ---
    elif user_data.get('awaiting_broadcast_message'):
        message_to_broadcast = update.message # The entire message object
        target_type = user_data.get('broadcast_target_type')
        specific_user_id = user_data.get('broadcast_specific_user_id') # Will be None if not specific

        if not target_type:
            logger.error(f"Admin {admin_id} sent a message while awaiting_broadcast_message=True, but target_type is missing!")
            user_data.pop('awaiting_broadcast_message', None) # Clear state
            await update.message.reply_text("❌ حدث خطأ داخلي (لم يتم تحديد الجمهور). تم إلغاء البث.")
            return

        logger.info(f"Admin {admin_id} provided broadcast message. Target: {target_type}, Specific ID: {specific_user_id}")

        # Clear state *before* starting the potentially long broadcast
        user_data.pop('awaiting_broadcast_message', None)
        user_data.pop('broadcast_target_type', None)
        user_data.pop('broadcast_specific_user_id', None)

        # Send a confirmation that the broadcast is starting
        await update.message.reply_text("⏳ جارٍ بدء عملية بث الرسالة... قد يستغرق هذا بعض الوقت حسب عدد المستخدمين.")

        # Execute the broadcast asynchronously
        success, failed = await execute_broadcast(context, message_to_broadcast, target_type, specific_user_id)

        # Send final report to admin
        report_message = f"✅ انتهى البث!\n\n"
        report_message += f"👥 الجمهور المستهدف: {target_type}"
        if specific_user_id:
            report_message += f" (المستخدم: {specific_user_id})"
        report_message += f"\n👍🏼 تم الإرسال بنجاح إلى: {success} مستخدم."
        report_message += f"\n👎🏼 فشل الإرسال إلى: {failed} مستخدم (محظورين أو غير نشطين)."

        await context.bot.send_message(chat_id=admin_id, text=report_message)
        logger.info(f"Broadcast report sent to admin {admin_id}.")

        return # End processing for broadcast message input

    # If the message is from the admin but no broadcast state is active, just ignore it here
    # (or let other handlers process it if needed).
    # logger.debug(f"Ignoring message from admin {admin_id} as no broadcast state is active.")

async def admin_broadcast_cancel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Cancels the broadcast process and clears state."""
    query = update.callback_query
    if str(query.from_user.id) != YOUR_CHAT_ID:
        await query.answer("❌ ليس لديك صلاحية الوصول!", show_alert=True)
        return

    # Clear all broadcast-related state
    context.user_data.pop('awaiting_broadcast_target', None)
    context.user_data.pop('awaiting_broadcast_user_id', None)
    context.user_data.pop('awaiting_broadcast_message', None)
    context.user_data.pop('broadcast_target_type', None)
    context.user_data.pop('broadcast_specific_user_id', None)

    await query.answer("✅ تم إلغاء عملية البث.")
    logger.info(f"Admin {query.from_user.id} cancelled broadcast.")
    # Go back to the main admin panel
    await admin_panel(update, context)

async def admin_toggle_preview_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles the admin toggling the admin preview requirement."""
    query = update.callback_query
    user = query.from_user

    # Security Check
    if str(user.id) != YOUR_CHAT_ID:
        await query.answer("❌ ليس لديك صلاحية الوصول!", show_alert=True)
        return

    # Load current settings
    current_settings = load_admin_settings()
    current_state = current_settings.get("admin_preview_required", True)

    # Toggle the state
    new_state = not current_state
    current_settings["admin_preview_required"] = new_state

    # Save the new settings
    save_admin_settings(current_settings)

    action_text = "إيقاف" if new_state is False else "تفعيل"
    logger.info(f"Admin {user.id} toggled admin preview requirement to: {new_state}")
    await query.answer(f"✅ تم {action_text} معاينة المسؤول.", show_alert=False)

    # Refresh the admin panel to show the updated button text
    # Need to create a 'fake' update object to pass to admin_panel
    fake_update_for_panel = Update(update.update_id, callback_query=query)
    await admin_panel(fake_update_for_panel, context)

async def handle_admin_actions(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """معالجة إجراءات واجهة الإدارة الرئيسية"""
    query = update.callback_query
    await query.answer()

    user = query.from_user
    if str(user.id) != YOUR_CHAT_ID:
        await update_ui_response(query, "❌ ليس لديك صلاحية الوصول!")
        return

    data = query.data

    if data == "admin_stats":
        await show_admin_stats(query, context)
    elif data == "admin_list_users": # Changed from admin_delete_user
        await show_user_list_for_admin(query, context)
    elif data == "admin_republish_list": # Changed from admin_republish
        await show_mod_list_for_admin(query, context, action="republish")
    elif data == "admin_delete_mod_list": # Changed from admin_delete_mod
        await show_mod_list_for_admin(query, context, action="delete_mod")
    elif data == "admin_restart_publishing": # <-- معالجة الزر الجديد
        await admin_restart_publishing_handler(query, context)
    # elif data == "admin_edit_mod":
    #     await request_mod_to_edit(query, context)
    elif data == "back_to_admin":
        # Need to pass update object for admin_panel
        fake_update = Update(update.update_id, callback_query=query)
        await admin_panel(fake_update, context)

async def show_admin_stats(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض إحصائيات البوت"""
    # إذا تم استدعاؤها من CallbackQueryHandler
    if hasattr(update, 'callback_query') and update.callback_query:
        query = update.callback_query
        await query.answer()
    else:
        # إذا تم استدعاؤها مباشرة مع query
        query = update
    user_channels = load_user_channels()
    all_users = load_all_users()  # تحميل جميع المستخدمين
    mods = load_mods() # تحميل من mods.json فقط
    # mods_en = load_mods("en") # <-- حذف
    pending_mods = load_pending_publications()
    feedback = load_user_feedback()

    # unique_mod_ids = set(m['id'] for m in mods_ar) | set(m['id'] for m in mods_en) # <-- تعديل
    unique_mod_ids = set(m['id'] for m in mods) # <-- بسيط الآن

    # حساب إحصائيات المستخدمين
    total_users = len(set(list(all_users.keys()) + list(user_channels.keys())))  # جميع المستخدمين (مع إزالة التكرار)
    users_with_channels = len(user_channels)  # المستخدمين الذين لديهم قنوات
    users_without_channels = total_users - users_with_channels  # المستخدمين بدون قنوات

    stats_text = f"📊 <b>إحصائيات البوت:</b>\n\n"
    stats_text += f"👥 إجمالي المستخدمين: {total_users}\n"
    stats_text += f"   📢 لديهم قنوات: {users_with_channels}\n"
    stats_text += f"   👤 بدون قنوات: {users_without_channels}\n\n"

    active_channels = sum(1 for u in user_channels.values() if isinstance(u, dict) and u.get("active"))
    stats_text += f"📢 القنوات النشطة حالياً: {active_channels}\n"
    stats_text += f"💾 إجمالي المودات الفريدة: {len(unique_mod_ids)}\n" # <-- بسيط الآن
    # stats_text += f"   (العربية: {len(mods_ar)}, الإنجليزية: {len(mods_en)})\n" # <-- حذف
    stats_text += f"⏳ مودات في قائمة الانتظار: {len(pending_mods)}\n"
    stats_text += f"📝 تقييمات المستخدمين المسجلة: {len(feedback)}\n"

    await update_ui_response(
        query,
        stats_text,
        is_photo=False,
        reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="back_to_admin")]])
    )

async def show_user_list_for_admin(update: Update, context: ContextTypes.DEFAULT_TYPE, page=1):
    """عرض قائمة المستخدمين للمسؤول مع ترقيم الصفحات"""
    # إذا تم استدعاؤها من CallbackQueryHandler
    if hasattr(update, 'callback_query') and update.callback_query:
        query = update.callback_query
        await query.answer()
    else:
        # إذا تم استدعاؤها مباشرة مع query
        query = update
    user_channels = load_user_channels()
    all_users = load_all_users()

    # دمج جميع المستخدمين من كلا الملفين
    all_user_ids = list(set(list(all_users.keys()) + list(user_channels.keys())))
    users_per_page = 5

    if not all_user_ids:
        await update_ui_response(
            query,
            "❌ لا يوجد مستخدمين مسجلين حالياً!",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة", callback_data="back_to_admin")]])
        )
        return

    start_index = (page - 1) * users_per_page
    end_index = start_index + users_per_page
    paginated_user_ids = all_user_ids[start_index:end_index]

    keyboard = []
    for user_id in paginated_user_ids:
        user_info_text = f"👤 {user_id}"

        # معلومات من ملف القنوات
        user_data = user_channels.get(user_id)
        channel_id = user_data.get("channel_id", "N/A") if isinstance(user_data, dict) else "N/A"

        # معلومات من ملف جميع المستخدمين
        all_user_data = all_users.get(user_id, {})
        username = all_user_data.get("username", "")

        if username:
            user_info_text += f" (@{username})"

        if channel_id != "N/A":
            user_info_text += f" 📢"
        else:
            user_info_text += f" 👤"
        keyboard.append([InlineKeyboardButton(user_info_text, callback_data=f"admin_view_user_{user_id}")])

    # إضافة أزرار التنقل
    pagination_row = []
    if page > 1:
        pagination_row.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"admin_list_users_{page-1}"))
    if end_index < len(all_user_ids):
        pagination_row.append(InlineKeyboardButton("التالي ➡️", callback_data=f"admin_list_users_{page+1}"))

    if pagination_row:
        keyboard.append(pagination_row)

    keyboard.append([InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="back_to_admin")])

    # تكوين النص
    total_pages = (len(all_user_ids) + users_per_page - 1) // users_per_page
    message_text = f"👥 <b>قائمة المستخدمين</b>\n\n"
    message_text += f"📊 <b>إجمالي المستخدمين:</b> {len(all_user_ids)}\n"
    message_text += f"   📢 لديهم قنوات: {len(user_channels)}\n"
    message_text += f"   👤 بدون قنوات: {len(all_user_ids) - len(user_channels)}\n"
    message_text += f"📄 <b>الصفحة:</b> {page} من {total_pages}\n\n"
    message_text += "اختر مستخدماً لعرض تفاصيله:"

    await update_ui_response(
        query,
        message_text,
        is_photo=False,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode="HTML"
    )

async def admin_view_user_details(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض تفاصيل مستخدم واحد للمسؤول وإجراءات الحذف"""
    query = update.callback_query
    await query.answer()
    user_id_to_view = query.data.split("_")[-1]

    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_to_view)

    if not user_data or not isinstance(user_data, dict):
        await update_ui_response(query, "❌ المستخدم غير موجود أو بياناته غير صالحة.", reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 قائمة المستخدمين", callback_data="admin_list_users")]]))
        return

    # Attempt to get user info from Telegram
    user_info_str = f"👤 <b>المستخدم:</b> <code>{user_id_to_view}</code>\n"
    try:
        chat = await context.bot.get_chat(user_id_to_view)
        user_info_str += f"   - الاسم: {chat.full_name or 'N/A'}\n"
        user_info_str += f"   - المعرف: @{chat.username or 'N/A'}\n"
    except Exception as e:
        logger.warning(f"Could not fetch user info for {user_id_to_view}: {e}")
        user_info_str += "   - (تعذر جلب معلومات المستخدم)\n"

    user_info_str += f"🌐 <b>اللغة:</b> {user_data.get('lang', 'ar')}\n"
    user_info_str += f"📢 <b>القناة:</b> <code>{user_data.get('channel_id', 'لم تربط')}</code>\n"
    user_info_str += f"⏱ <b>الفاصل:</b> {user_data.get('publish_interval', 'N/A')} دقيقة\n"
    user_info_str += f"👁️ <b>المعاينة مفعلة:</b> {'نعم ✅' if user_data.get('preview_enabled', False) else 'لا ❌'}\n"
    user_info_str += f"⏯️ <b>النشر مفعل:</b> {'نعم ✅' if user_data.get('active', True) else 'لا ❌'}\n"
    user_info_str += f"🕒 <b>آخر نشر:</b> {user_data.get('last_publish_time', 'لم يتم')}\n"


    keyboard = [
        [InlineKeyboardButton("🗑 حذف هذا المستخدم", callback_data=f"admin_delete_confirm_{user_id_to_view}")],
        [InlineKeyboardButton("🔙 العودة لقائمة المستخدمين", callback_data="admin_list_users")]
    ]

    await update_ui_response(query, user_info_str, is_photo=False, reply_markup=InlineKeyboardMarkup(keyboard))


async def handle_admin_user_list_page(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles pagination for the admin user list."""
    query = update.callback_query
    await query.answer()
    page = int(query.data.split("_")[-1])
    await show_user_list_for_admin(update, context, page=page)


async def admin_delete_user_confirmation(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Ask for confirmation before deleting a user."""
    query = update.callback_query
    await query.answer()
    user_id_to_delete = query.data.split("_")[-1]

    keyboard = [
        [
            InlineKeyboardButton("✅ نعم، حذف المستخدم نهائياً", callback_data=f"admin_delete_execute_{user_id_to_delete}"),
            InlineKeyboardButton("❌ إلغاء", callback_data=f"admin_view_user_{user_id_to_delete}") # Back to user details
        ]
    ]
    await update_ui_response(
        query,
        f"⚠️ <b>تحذير!</b> هل أنت متأكد من رغبتك في حذف المستخدم <code>{user_id_to_delete}</code>؟\nسيتم حذف جميع بياناته وإيقاف النشر لقناته.",
        is_photo=False,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def admin_delete_user_execute(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Deletes the user data after admin confirmation."""
    query = update.callback_query
    await query.answer()
    user_id_to_delete = query.data.split("_")[-1]
    logger.warning(f"Admin {query.from_user.id} requested deletion of user {user_id_to_delete}")

    user_channels = load_user_channels()
    old_channel_id = None
    if user_id_to_delete in user_channels:
        # Get old channel ID for queue cleaning
        if isinstance(user_channels[user_id_to_delete], dict):
            old_channel_id = user_channels[user_id_to_delete].get("channel_id")

        del user_channels[user_id_to_delete]
        save_json_file(USER_CHANNELS_FILE, user_channels)

        # حذف المستخدم من ملف جميع المستخدمين
        all_users = load_all_users()
        if user_id_to_delete in all_users:
            del all_users[user_id_to_delete]
            save_json_file(ALL_USERS_FILE, all_users)
            logger.info(f"Removed user {user_id_to_delete} from all_users file")

        # حذف التقييمات المرتبطة
        feedback = load_user_feedback()
        keys_to_delete = [k for k in feedback.keys() if k.startswith(f"{user_id_to_delete}_")]
        if keys_to_delete:
            for k in keys_to_delete:
                del feedback[k]
            save_json_file(USER_FEEDBACK_FILE, feedback)
            logger.info(f"Removed {len(keys_to_delete)} feedback entries for deleted user {user_id_to_delete}")

        # حذف حالة المودات المنشورة
        mods_status = load_user_mods_status()
        if user_id_to_delete in mods_status:
            del mods_status[user_id_to_delete]
            save_json_file(USER_MODS_STATUS_FILE, mods_status)
            logger.info(f"Removed mod status entries for deleted user {user_id_to_delete}")

        # تنظيف قائمة الانتظار
        # Pass only user_id to indicate user deletion
        clean_pending_queue(user_id_to_delete)

        await update_ui_response(
            query,
            f"✅ تم حذف المستخدم <code>{user_id_to_delete}</code> وجميع بياناته بنجاح.",
            is_photo=False,
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة لقائمة المستخدمين", callback_data="admin_list_users")]])
        )
        # Notify admin
        await send_notification(f"🗑 المستخدم {user_id_to_delete} تم حذفه بواسطة المسؤول.", context)
    else:
        await update_ui_response(
            query,
            "❌ المستخدم غير موجود (ربما تم حذفه بالفعل).",
            is_photo=False,
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 قائمة المستخدمين", callback_data="admin_list_users")]])
        )

# --- Mod Management (Admin) ---

from telegram import InlineKeyboardButton, InlineKeyboardMarkup
# تأكد من أن لديك دالة update_ui_response ودالة load_mods المعدلة متاحة في النطاق
# from your_bot_module import update_ui_response, load_mods

async def show_mod_list_for_admin(update: Update, context: ContextTypes.DEFAULT_TYPE, action=None, page=1):
    """
    يعرض قائمة مودات مرقمة الصفحات لإجراءات المسؤول (إعادة النشر/الحذف).
    يستخدم الآن دالة load_mods() المحدثة التي تقرأ من mods.json فقط.
    """
    # إذا تم استدعاؤها من CallbackQueryHandler
    if hasattr(update, 'callback_query') and update.callback_query:
        query = update.callback_query
        await query.answer()
        # استخراج action من callback_data إذا لم يتم تمريره
        if action is None:
            if "republish" in query.data:
                action = "republish"
            elif "delete_mod" in query.data:
                action = "delete_mod"
    else:
        # إذا تم استدعاؤها مباشرة مع query
        query = update
    # --- تحميل المودات من المصدر الموحد ---
    all_mods = load_mods() # استدعاء دالة التحميل المحدثة
    # -----------------------------------

    # --- إنشاء قائمة مرتبة مباشرة من المودات المحملة ---
    # يتم الفرز حسب معرف المود (ID) تنازلياً لإظهار الأحدث أولاً
    sorted_mod_items = sorted(
        [(mod['id'], mod['title']) for mod in all_mods],  # استخراج (id, title)
        key=lambda item: item[0],                         # الفرز حسب الـ ID
        reverse=True                                      # الأكبر (الأحدث غالباً) أولاً
    )
    # ------------------------------------------------

    mods_per_page = 5 # عدد المودات في كل صفحة

    # التعامل مع حالة عدم وجود مودات
    if not sorted_mod_items:
        await update_ui_response(
            query,
            "❌ لا يوجد مودات متاحة!",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة", callback_data="back_to_admin")]])
        )
        return

    # حساب فهارس المودات للصفحة الحالية
    start_index = (page - 1) * mods_per_page
    end_index = start_index + mods_per_page
    paginated_mods = sorted_mod_items[start_index:end_index]

    # بناء أزرار القائمة
    keyboard = []
    for mod_id, mod_title in paginated_mods:
        # قص العنوان إذا كان طويلاً جداً للعرض
        display_title = mod_title[:40] + "..." if len(mod_title) > 40 else mod_title
        # إضافة زر لكل مود
        keyboard.append([InlineKeyboardButton(f"ID:{mod_id} - {display_title}", callback_data=f"{action}_{mod_id}")])

    # إضافة أزرار التنقل بين الصفحات
    pagination_row = []
    if page > 1: # زر السابق إذا لم تكن الصفحة الأولى
        pagination_row.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"{action}_list_{page-1}"))
    if end_index < len(sorted_mod_items): # زر التالي إذا كان هناك المزيد من المودات
        pagination_row.append(InlineKeyboardButton("التالي ➡️", callback_data=f"{action}_list_{page+1}"))

    # إضافة صف أزرار التنقل إذا وُجدت
    if pagination_row:
        keyboard.append(pagination_row)

    # إضافة زر العودة للوحة التحكم الرئيسية للمسؤول
    keyboard.append([InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="back_to_admin")])

    # تحديد نص الإجراء (حذف أو إعادة نشر)
    action_text = "لإعادة النشر" if action == "republish" else "للحذف"

    # تحديث رسالة المستخدم لعرض قائمة المودات
    await update_ui_response(
        query,
        f"اختر المود {action_text} (صفحة {page}):",
        is_photo=False, # نفترض أن هذه الواجهة تظهر كرد على رسالة نصية
        reply_markup=InlineKeyboardMarkup(keyboard)
    )


async def handle_admin_mod_list_page(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles pagination for the admin mod lists."""
    query = update.callback_query
    await query.answer()
    parts = query.data.split("_") # e.g., republish_list_2 or delete_mod_list_3
    action = parts[0] if parts[1] == "list" else parts[0] + "_" + parts[1] # Handles 'republish' and 'delete_mod'
    page = int(parts[-1])
    await show_mod_list_for_admin(update, context, action=action, page=page)


async def republish_mod_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handles the admin request to republish a specific mod.
    Corrected to use unified load_mods().
    """
    query = update.callback_query
    await query.answer("⏳ جارٍ إعادة النشر...", show_alert=False)

    try:
        mod_id = int(query.data.split("_")[1])
    except (IndexError, ValueError):
        logger.error(f"Invalid callback data for republish: {query.data}")
        await update_ui_response(query, "❌ خطأ في بيانات الاستدعاء.", reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة", callback_data="admin_panel")]]))
        return

    logger.warning(f"Admin {query.from_user.id} requested republishing of mod {mod_id}")

    # *** START CORRECTION ***
    # Load mod data from the unified source
    all_mods = load_mods() # Load all mods
    mod_data = next((m for m in all_mods if m.get('id') == mod_id), None) # Find the specific mod
    # *** END CORRECTION ***

    if not mod_data:
         await update_ui_response(
             query,
             f"❌ المود {mod_id} غير موجود في ملف المودات!",
             reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 قائمة المودات", callback_data="admin_republish_list")]])
         )
         return

    mod_title = mod_data.get('title', '[بدون عنوان]')

    # --- Reset admin processed status ---
    admin_processed = load_admin_processed_mods()
    if mod_id in admin_processed:
        admin_processed.remove(mod_id)
        save_json_file(ADMIN_PROCESSED_MODS_FILE, list(admin_processed))
        logger.info(f"Removed mod {mod_id} from admin processed list for republishing.")

    # --- Reset published status for all users ---
    user_mods_status = load_user_mods_status()
    users_reset_count = 0
    status_changed = False
    for user_id_str in list(user_mods_status.keys()):
        if isinstance(user_mods_status.get(user_id_str), list) and mod_id in user_mods_status[user_id_str]:
            user_mods_status[user_id_str].remove(mod_id)
            users_reset_count += 1
            status_changed = True
    if status_changed:
        save_json_file(USER_MODS_STATUS_FILE, user_mods_status)
        logger.info(f"Reset published status for mod {mod_id} for {users_reset_count} users.")

    # --- Reset user feedback ---
    user_feedback = load_user_feedback()
    feedback_keys_to_remove = [key for key in user_feedback if key.endswith(f"_{mod_id}")]
    feedback_changed = False
    if feedback_keys_to_remove:
        for key in feedback_keys_to_remove:
            if key in user_feedback:
                 del user_feedback[key]
                 feedback_changed = True
        if feedback_changed:
            save_json_file(USER_FEEDBACK_FILE, user_feedback)
            logger.info(f"Reset {len(feedback_keys_to_remove)} user feedback entries for mod {mod_id}.")

    # --- Remove from pending queue ---
    pending = load_pending_publications()
    original_pending_count = len(pending)
    new_pending = [item for item in pending if item.get('mod_id') != mod_id]
    if len(new_pending) < original_pending_count:
        save_pending_publications(new_pending)
        logger.info(f"Removed mod {mod_id} from pending queue for republishing.")

    # --- Remove from user block lists ---
    user_blocked_mods = load_user_blocked_mods()
    blocked_changed = False
    for user_id_str in list(user_blocked_mods.keys()):
        if isinstance(user_blocked_mods.get(user_id_str), list) and mod_id in user_blocked_mods[user_id_str]:
            user_blocked_mods[user_id_str].remove(mod_id)
            blocked_changed = True
            logger.info(f"Removed mod {mod_id} from block list of user {user_id_str}.")
    if blocked_changed:
        save_json_file(USER_BLOCKED_MODS_FILE, user_blocked_mods)


    # --- Send mod back to admin for general preview (this step might be less useful now) ---
    # The mod will naturally be picked up by `check_users_and_propose_mods` for eligible users.
    # Sending a general preview might confuse the admin.
    # Let's comment out the general preview sending.
    # if mod_data:
    #     # await preview_mod_before_publish(mod_data, context) # <-- Commented out
    #     logger.info(f"Republish requested for {mod_id}. It will be proposed to users when their schedule is due.")
    # else:
    #      logger.error(f"Mod data not found for {mod_id} during republish final steps.") # Should not happen

    await update_ui_response(
        query,
        f"✅ تم إعداد المود <code>{mod_id}</code> ({html.escape(mod_title)}) لإعادة النشر.\nسيتم اقتراحه للمستخدمين المؤهلين عندما يحين دورهم.",
        is_photo=False,
        reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 قائمة المودات", callback_data="admin_republish_list")]])
    )

async def delete_mod_confirmation(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """ Ask for confirmation before deleting a mod."""
    query = update.callback_query
    mod_id = int(query.data.split("_")[-1]) # delete_mod_123

    # Get mod title for confirmation message
    mods_ar = load_mods("ar")
    mod_title = next((m['title'] for m in mods_ar if m['id'] == mod_id), None)
    if not mod_title:
        mods_en = load_mods("en")
        mod_title = next((m['title'] for m in mods_en if m['id'] == mod_id), f"ID:{mod_id}")


    keyboard = [
        [
            InlineKeyboardButton("✅ نعم، حذف المود نهائياً", callback_data=f"delete_mod_execute_{mod_id}"),
            InlineKeyboardButton("❌ إلغاء", callback_data="admin_delete_mod_list") # Back to mod list
        ]
    ]
    await update_ui_response(
        query,
        f"⚠️ <b>تحذير!</b> هل أنت متأكد من حذف المود:\n<code>{mod_id} - {mod_title}</code>؟\nسيتم حذفه من كلا اللغتين وجميع بيانات المستخدمين المتعلقة به.",
        is_photo=False,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def delete_mod_execute(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Deletes the specified mod permanently after admin confirmation.
    Corrected to use unified load_mods().
    """
    query = update.callback_query
    await query.answer("⏳ جارٍ الحذف...", show_alert=False)

    try:
        mod_id = int(query.data.split("_")[-1]) # delete_mod_execute_123
    except (IndexError, ValueError):
        logger.error(f"Invalid callback data for delete_mod_execute: {query.data}")
        await update_ui_response(
            query,
            "❌ خطأ في بيانات الاستدعاء.",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة", callback_data="admin_panel")]])
           )
        return

    logger.warning(f"Admin {query.from_user.id} requested deletion of mod {mod_id}")

    # *** START CORRECTION ***
    # Load mod from Supabase to find the one to delete and get its title
    mod_to_delete = get_mod_by_id(mod_id)
    # *** END CORRECTION ***

    if not mod_to_delete:
         await update_ui_response(
             query,
             f"❌ المود رقم {mod_id} غير موجود في قاعدة البيانات!",
             reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 قائمة الحذف", callback_data="admin_delete_mod_list")]])
             )
         return

    mod_title = mod_to_delete.get('title', f"ID:{mod_id}")

    # --- Delete from Supabase database ---
    mods_deleted_from_db = delete_mod_from_db(mod_id)

    if mods_deleted_from_db:
        logger.info(f"Removed mod {mod_id} from Supabase database.")
    else:
        logger.warning(f"Mod {mod_id} was not found in Supabase database during deletion.")
        # Continue cleanup even if not found in database, as it might exist in other status files

    # --- Continue cleanup ---
    # Remove from admin processed list
    admin_processed = load_admin_processed_mods()
    if mod_id in admin_processed:
        admin_processed.remove(mod_id)
        save_json_file(ADMIN_PROCESSED_MODS_FILE, list(admin_processed))
        logger.debug(f"Removed mod {mod_id} from admin processed list.")

    # Remove from user feedback
    user_feedback = load_user_feedback()
    keys_to_delete_fb = [k for k in user_feedback if k.endswith(f"_{mod_id}")]
    fb_changed = False
    if keys_to_delete_fb:
        for k in keys_to_delete_fb:
            if k in user_feedback:
                del user_feedback[k]
                fb_changed = True
        if fb_changed:
            save_json_file(USER_FEEDBACK_FILE, user_feedback)
            logger.debug(f"Removed {len(keys_to_delete_fb)} feedback entries for mod {mod_id}.")

    # Remove from user published status
    mods_status = load_user_mods_status()
    users_updated_ms = 0
    ms_changed = False
    for user_id in list(mods_status.keys()):
        if isinstance(mods_status.get(user_id), list) and mod_id in mods_status[user_id]:
            mods_status[user_id].remove(mod_id)
            users_updated_ms += 1
            ms_changed = True
    if ms_changed:
        save_json_file(USER_MODS_STATUS_FILE, mods_status)
        logger.debug(f"Removed mod {mod_id} from status list for {users_updated_ms} users.")

    # Remove from pending queue
    pending = load_pending_publications()
    original_pending_count = len(pending)
    new_pending = [item for item in pending if item.get('mod_id') != mod_id]
    if len(new_pending) < original_pending_count:
        save_pending_publications(new_pending)
        logger.debug(f"Removed mod {mod_id} from pending queue.")

    # --- Remove from user block lists ---
    user_blocked_mods = load_user_blocked_mods()
    blocked_changed = False
    for user_id_str in list(user_blocked_mods.keys()):
        if isinstance(user_blocked_mods.get(user_id_str), list) and mod_id in user_blocked_mods[user_id_str]:
            user_blocked_mods[user_id_str].remove(mod_id)
            blocked_changed = True
            logger.info(f"Removed deleted mod {mod_id} from block list of user {user_id_str}.")
    if blocked_changed:
        save_json_file(USER_BLOCKED_MODS_FILE, user_blocked_mods)

    # --- Final Confirmation ---
    escaped_title = html.escape(mod_title)
    await update_ui_response(
        query,
        f"✅ تم حذف المود <code>{mod_id} - {escaped_title}</code> بنجاح من جميع البيانات.",
        is_photo=bool(query.message and query.message.photo), # Preserve original message type
        reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 قائمة الحذف", callback_data="admin_delete_mod_list")]])
        )
    await send_notification(f"🗑 تم حذف المود {mod_id} ({escaped_title}) بواسطة المسؤول.", context)
# --- Preview Settings (User) ---

async def admin_restart_publishing_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Clears all pending admin approvals."""
    query = update.callback_query
    await query.answer()
    logger.warning(f"Admin {query.from_user.id} triggered 'Restart Publishing' (Clearing pending admin approvals)")
    pending = load_pending_publications()
    original_count = len(pending)
    cleared_count = 0

    # Filter out items awaiting admin approval
    new_pending = []
    for item in pending:
        if item.get("status") == "awaiting_admin_approval":
            logger.info(f"Clearing pending admin approval for mod {item.get('mod_id')} / user {item.get('user_id')}")
            cleared_count += 1
        else:
            new_pending.append(item) # Keep other statuses

    if cleared_count > 0:
        save_pending_publications(new_pending)
        await query.answer(f"✅ تم مسح {cleared_count} طلب موافقة معلق.", show_alert=True)
        # Optionally update the admin panel message or go back
        await update_ui_response(query, query.message.text + f"\n\n✅ تم مسح {cleared_count} طلب موافقة.", is_photo=False) # Append to existing text
    else:
        await query.answer("ℹ️ لا توجد طلبات موافقة معلقة لمسحها.", show_alert=True)

# --- في دالة main()، أضف المعالج لهذا الزر ---
# داخل `main()`، بعد تسجيل المعالجات الأخرى:


async def preview_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تبديل إعداد معاينة المودات قبل النشر"""
    # This seems less useful now that preview is a per-channel setting
    # Kept for potential future use or if logic changes back
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str)

    if not isinstance(user_data, dict):
         await query.answer("❌ خطأ: لم يتم العثور على إعداداتك.", show_alert=True)
         return

    current_status = user_data.get("preview_enabled", False)
    new_status = not current_status
    user_data["preview_enabled"] = new_status
    save_json_file(USER_CHANNELS_FILE, user_channels) # Save updated data

    lang = get_user_lang(user_id)
    texts = {
        "ar": {
            "enabled": "✅ تم تفعيل المعاينة",
            "disabled": "❌ تم إيقاف المعاينة",
            "description": "سيتم إرسال المودات لك للمراجعة قبل نشرها في القناة.",
            "back": "العودة"
        },
        "en": {
            "enabled": "✅ Preview enabled",
            "disabled": "❌ Preview disabled",
            "description": "Mods will be sent to you for review before publishing.",
            "back": "Back"
        }
    }
    text = texts.get(lang, texts['ar'])

    status_text = text["enabled"] if new_status else text["disabled"]
    await update_ui_response(
        query,
        f"{status_text}\n{text['description']}",
        is_photo=False,
        reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(text["back"], callback_data="show_channel_settings")]]) # Go back to channel settings
    )


# --- Helper Functions (Data Loading/Saving/Access) ---

def load_user_channels():
    data = load_json_file(USER_CHANNELS_FILE, {})
    # Ensure all entries are dictionaries with default values
    processed_data = {}
    if isinstance(data, dict): # Check if loaded data is a dictionary
        for k, v in data.items():
            if isinstance(v, dict):
                # Ensure all keys exist with defaults
                v.setdefault('lang', 'ar')
                v.setdefault('publish_interval', 60) # Default 60 mins
                v.setdefault('active', True)
                v.setdefault('preview_enabled', False)
                v.setdefault('last_publish_time', None)
                v.setdefault('channel_id', None) # Important
                v.setdefault('mod_categories', ['addons', 'shaders', 'texture_packs', 'seeds', 'maps']) # Default: all categories
                v.setdefault('mc_versions', ['1.21+', '1.20+', '1.19+', '1.18+']) # Default: all versions
                v.setdefault('message_format', 'classic') # Default: classic format
                v.setdefault('channel_lang', None) # Default: use user's main language
                processed_data[k] = v
            elif isinstance(v, str): # Handle old format
                 processed_data[k] = {
                    "channel_id": v,
                    "lang": "ar",
                    "publish_interval": 60,
                    "active": True,
                    "preview_enabled": False,
                    "last_publish_time": None,
                    "mod_categories": ['addons', 'shaders', 'texture_packs', 'seeds', 'maps'], # Default: all categories
                    "mc_versions": ['1.21+', '1.20+', '1.19+', '1.18+'], # Default: all versions
                    "message_format": "classic", # Default: classic format
                    "channel_lang": None # Default: use user's main language
                }
            else:
                logger.warning(f"Invalid data format for user {k} in {USER_CHANNELS_FILE}, skipping.")
    else:
        logger.error(f"Root structure in {USER_CHANNELS_FILE} is not a dictionary. Resetting to empty.")
        return {} # Return empty if file is corrupt at root level
    return processed_data


def save_user_channel(user_id: int, channel_id: str, publish_interval_minutes: int = 60, lang: str = 'ar'):
    """حفظ معلومات قناة المستخدم مع فاصل زمني للنشر (بالدقائق) واللغة"""
    user_channels = load_user_channels()
    user_id_str = str(user_id)
    old_channel_id = None # Initialize old channel id

    # Preserve existing settings if user exists
    user_data = user_channels.get(user_id_str, {})
    if not isinstance(user_data, dict): # Handle potential old format remnants
        logger.warning(f"Converting non-dict data for user {user_id_str} in save_user_channel.")
        # Create a new dict but try to preserve old channel ID if it was a string
        old_channel_id = user_data if isinstance(user_data, str) else None
        user_data = {} # Start fresh dict
    else:
        # Store the old channel ID *before* updating
        old_channel_id = user_data.get("channel_id")

    # Update user data, ensuring defaults are kept if not provided
    user_data.update({
        "channel_id": channel_id,
        "publish_interval": publish_interval_minutes,
        "lang": lang,
        "active": user_data.get("active", True), # Keep existing status or default to True
        "preview_enabled": user_data.get("preview_enabled", False), # Keep existing or default to False
        "last_publish_time": user_data.get("last_publish_time", None) # Keep existing time
    })

    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels) # Use safe save

    # Trigger cleanup if channel ID changed AND the old ID existed
    if old_channel_id is not None and old_channel_id != channel_id:
        logger.info(f"Channel ID changed for user {user_id_str}. Old: {old_channel_id}, New: {channel_id}. Triggering pending queue cleanup.")
        # Pass string IDs to ensure correct type
        clean_pending_queue(user_id_str, old_channel_id=str(old_channel_id), new_channel_id=str(channel_id))
    elif old_channel_id is None and channel_id is not None:
         logger.info(f"New channel ID {channel_id} added for user {user_id_str}. No queue cleanup needed for channel change.")


def load_mods():
    """
    تحميل المودات من قاعدة بيانات Supabase.
    يجب أن يحتوي كل مود على كائن description بداخله مفاتيح 'ar' و 'en'.
    """
    logger.debug("جاري تحميل المودات من Supabase...")

    try:
        # جلب المودات من Supabase
        mods = get_all_mods()

        if not mods:
            logger.warning("لم يتم العثور على أي مودات في قاعدة البيانات")
            return []

        # التحقق من صحة البيانات
        valid_mods = []
        seen_ids = set()
        invalid_count = 0
        required_fields = ["id", "title", "description", "download_url"]

        for mod in mods:
            is_valid = True

            if not isinstance(mod, dict):
                logger.warning(f"تخطي عنصر غير صالح (ليس قاموس): {mod}")
                invalid_count += 1
                continue

            mod_id = mod.get("id")
            if mod_id is None or not isinstance(mod_id, int):
                logger.warning(f"مود مفقود أو معرف غير صالح: {mod}")
                invalid_count += 1
                is_valid = False
            elif mod_id in seen_ids:
                logger.warning(f"معرف مود مكرر {mod_id}، تخطي.")
                invalid_count += 1
                is_valid = False
            else:
                seen_ids.add(mod_id)

            # التحقق من الحقول الأساسية
            missing_fields = [key for key in required_fields if key not in mod or not mod[key]]
            if missing_fields:
                logger.warning(f"مود {mod_id or 'معرف غير معروف'} مفقود حقول مطلوبة ({', '.join(missing_fields)}): {mod}")
                invalid_count += 1
                is_valid = False

            # التحقق من حقل description
            description_obj = mod.get("description")
            if not isinstance(description_obj, dict) or not description_obj.get("ar") or not description_obj.get("en"):
                logger.warning(f"مود {mod_id or 'معرف غير معروف'} له وصف غير صالح (يجب أن يكون قاموس مع مفاتيح 'ar' و 'en'): {mod}")
                invalid_count += 1
                is_valid = False

            # التأكد من وجود image_url
            mod["image_url"] = mod.get("image_url") or None

            if is_valid:
                valid_mods.append(mod)

        if invalid_count > 0:
            logger.warning(f"تم العثور على {invalid_count} إدخالات غير صالحة أثناء تحميل المودات من Supabase.")

        logger.debug(f"تم تحميل {len(valid_mods)} مودات صالحة من Supabase")
        return valid_mods

    except Exception as e:
        logger.error(f"خطأ في تحميل المودات من Supabase: {e}")
        return []


async def send_notification(text: str, context: ContextTypes.DEFAULT_TYPE):
    """إرسال إشعار للمسؤول"""
    if not YOUR_CHAT_ID:
         logger.warning("Admin chat ID not set. Cannot send notification.")
         return
    try:
        await context.bot.send_message(
            chat_id=YOUR_CHAT_ID,
            text=text,
            parse_mode="HTML" # Allow basic formatting
        )
    except BadRequest as e:
         logger.error(f"Failed to send notification to admin {YOUR_CHAT_ID} (BadRequest): {e}")
    except TelegramError as e:
        logger.error(f"Failed to send notification to admin {YOUR_CHAT_ID} (TelegramError): {e}")
    except Exception as e:
         logger.error(f"فشل إرسال الإشعار للمسؤول {YOUR_CHAT_ID}: {str(e)}")

def update_publish_settings(user_id: int, publish_interval_minutes: int = None, is_active: bool = None, preview_enabled: bool = None, last_publish_time_str: str = None):
    """تحديث إعدادات النشر للمستخدم (الفاصل الزمني, الحالة, المعاينة, آخر وقت نشر)"""
    user_channels = load_user_channels()
    user_id_str = str(user_id)
    if user_id_str in user_channels:
        # Ensure it's a dictionary before updating
        if isinstance(user_channels[user_id_str], dict):
            updated = False
            if publish_interval_minutes is not None:
                 user_channels[user_id_str]["publish_interval"] = publish_interval_minutes
                 updated = True
            if is_active is not None:
                 user_channels[user_id_str]["active"] = is_active
                 updated = True
            if preview_enabled is not None:
                 user_channels[user_id_str]["preview_enabled"] = preview_enabled
                 updated = True
            # Add condition to update last_publish_time
            if last_publish_time_str is not None:
                 user_channels[user_id_str]["last_publish_time"] = last_publish_time_str
                 updated = True

            if updated:
                 save_json_file(USER_CHANNELS_FILE, user_channels)
            return True
        else:
             logger.error(f"Cannot update settings for user {user_id_str}, data is not a dictionary.")
             return False # Indicate failure
    logger.warning(f"Cannot update settings for non-existent user {user_id_str}")
    return False # Indicate user not found

# --- New Helper Function for Random Mod Selection ---
import random

def get_next_random_unpublished_mod(user_id_str: str, lang: str): # lang is still needed to know *which* description the user wants later
    """
    Finds a random mod not yet published, pending, blocked, or rejected by the user.
    Now also filters by user's selected mod categories.
    Returns the full mod data dictionary.
    """
    logger.debug(f"Searching for next random unpublished mod for user {user_id_str} (preferred lang: {lang})")

    # الحصول على التصنيفات والإصدارات المختارة للمستخدم
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    selected_categories = user_data.get('mod_categories', ['addons', 'shaders', 'texture_packs', 'seeds', 'maps'])
    selected_versions = user_data.get('mc_versions', ['1.21+', '1.20+', '1.19+', '1.18+'])

    logger.debug(f"User {user_id_str} selected categories: {selected_categories}")
    logger.debug(f"User {user_id_str} selected versions: {selected_versions}")

    # --- تحميل جميع المودات ---
    all_mods = load_mods() # لا نمرر اللغة هنا
    # -----------------------
    if not all_mods:
        logger.warning(f"No mods found in {MODS_FILE} to select next random mod.")
        return None

    # دالة مساعدة لمقارنة إصدارات Minecraft
    def is_version_compatible(mod_version, selected_versions):
        """تحقق من توافق إصدار المود مع الإصدارات المختارة"""
        if not mod_version:
            return True  # إذا لم يكن هناك إصدار محدد، اعتبره متوافق

        # تنظيف إصدار المود
        mod_version = mod_version.strip().lower()

        for selected_version in selected_versions:
            selected_version = selected_version.strip()

            if selected_version == '1.21+':
                # إصدار 1.21 وما فوق
                if any(v in mod_version for v in ['1.21', '1.22', '1.23', '1.24', '1.25']):
                    return True
            elif selected_version == '1.20+':
                # إصدار 1.20 وما فوق
                if any(v in mod_version for v in ['1.20', '1.21', '1.22', '1.23', '1.24', '1.25']):
                    return True
            elif selected_version == '1.19+':
                # إصدار 1.19 وما فوق
                if any(v in mod_version for v in ['1.19', '1.20', '1.21', '1.22', '1.23', '1.24', '1.25']):
                    return True
            elif selected_version == '1.18+':
                # إصدار 1.18 وما فوق
                if any(v in mod_version for v in ['1.18', '1.19', '1.20', '1.21', '1.22', '1.23', '1.24', '1.25']):
                    return True

        return False

    # فلترة المودات حسب التصنيفات والإصدارات المختارة
    filtered_mods = []
    for mod in all_mods:
        mod_category = mod.get('category', 'addons')  # افتراضياً addons إذا لم يكن محدد
        mod_version = mod.get('mc_version', '')  # إصدار Minecraft للمود

        # تحقق من التصنيف والإصدار
        if (mod_category in selected_categories and
            is_version_compatible(mod_version, selected_versions)):
            filtered_mods.append(mod)

    if not filtered_mods:
        logger.warning(f"No mods found matching user {user_id_str} selected categories: {selected_categories} and versions: {selected_versions}")
        return None

    logger.debug(f"Found {len(filtered_mods)} mods matching selected categories and versions out of {len(all_mods)} total mods")

    # الحصول على معرفات المودات المفلترة
    all_mod_ids = {mod['id'] for mod in filtered_mods}

    # ... باقي الكود للحصول على المودات المنشورة والمعلقة والمرفوضة والمحظورة للمستخدم يبقى كما هو ...
    user_mods_status = load_user_mods_status()
    published_ids = set(user_mods_status.get(user_id_str, []))
    pending_publications = load_pending_publications()
    pending_ids = {item['mod_id'] for item in pending_publications if item['user_id'] == user_id_str}
    user_feedback = load_user_feedback()
    rejected_ids = set()
    for key, approved in user_feedback.items():
        if key.startswith(f"{user_id_str}_") and not approved:
            try:
                rejected_mod_id = int(key.split("_")[1])
                rejected_ids.add(rejected_mod_id)
            except (ValueError, IndexError): continue
    user_blocked_mods = load_user_blocked_mods()
    blocked_ids = set(user_blocked_mods.get(user_id_str, []))
    processed_ids = published_ids.union(pending_ids).union(rejected_ids).union(blocked_ids)
    logger.debug(f"User {user_id_str}: Published={published_ids}, Pending={pending_ids}, Rejected={rejected_ids}, Blocked={blocked_ids}, Total Processed={processed_ids}")
    # ------------------------------------------------------------------------------------

    available_ids = list(all_mod_ids - processed_ids)
    logger.debug(f"User {user_id_str}: Available unpublished mod IDs: {available_ids}")

    if not available_ids:
        logger.info(f"No unpublished/unblocked mods available for user {user_id_str}.")
        return None

    next_mod_id = random.choice(available_ids)
    logger.info(f"Selected next random mod ID {next_mod_id} for user {user_id_str}")

    # --- إيجاد بيانات المود الكاملة من القائمة المفلترة ---
    next_mod_data = next((mod for mod in filtered_mods if mod['id'] == next_mod_id), None)
    # -------------------------------------------------

    if not next_mod_data:
         logger.error(f"Could not find mod data for randomly selected ID {next_mod_id} in filtered mods. This should not happen.")
         return None

    logger.debug(f"Selected mod {next_mod_id} from category '{next_mod_data.get('category', 'unknown')}' for user {user_id_str}")

    # --- إعادة المود الكامل ---
    return next_mod_data
    # ------------------------

# --- End New Helper Function ---


def is_preview_enabled(user_id):
    """التحقق مما إذا كان المستخدم فعّل ميزة المعاينة"""
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id))
    if isinstance(user_data, dict):
        return user_data.get("preview_enabled", False)
    return False

def get_user_lang(user_id):
    """Gets the user's preferred language, defaulting to 'ar'."""
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id))
    if isinstance(user_data, dict):
        return user_data.get('lang', 'ar')
    return 'ar'

# def get_user_channels(user_id): # Seems unused, user data is fetched directly where needed
#     """الحصول على بيانات قناة المستخدم (كقاموس)"""
#     user_channels_data = load_user_channels()
#     user_data = user_channels_data.get(str(user_id))
#
#     if isinstance(user_data, dict) and user_data.get("channel_id"):
#         return [user_data] # Return list containing the single user data dict
#     elif isinstance(user_data, str): # Handle old format just in case
#         return [{"channel_id": user_data, "lang": "ar", "publish_interval": 60, "active": True, "preview_enabled": False, "last_publish_time": None}]
#     return [] # Return empty list if no valid channel data found


# toggle_channel_status is replaced by update_publish_settings(is_active=...)

# --- Command Handlers ---

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles the /start command with bilingual initial prompt for new users."""
    try:
        user = update.effective_user
        user_id = user.id
        user_channels = load_user_channels()
        user_id_str = str(user_id)

        # --- تعريف الأزرار مرة واحدة ---
        keyboard = [
            [
                InlineKeyboardButton("🇸🇦 العربية - Arabic", callback_data="lang_ar"),
                InlineKeyboardButton("🇬🇧 English - الإنجليزية", callback_data="lang_en")
            ],
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        # --- التعامل مع المستخدم الجديد تمامًا ---
        if user_id_str not in user_channels:
            logger.info(f"New user started the bot: {user_id} | @{user.username or 'no-username'} | Name: {user.full_name or 'N/A'}")

            # حفظ المستخدم الجديد في ملف جميع المستخدمين
            user_info = {
                "lang": "ar",  # اللغة الافتراضية
                "username": user.username or "",
                "full_name": user.full_name or ""
            }
            save_user_to_all_users(user_id, user_info)

            # إرسال إشعار للمسؤول (مع استخدام html.escape)
            try:
                await send_notification(
                    f"👤 مستخدم جديد بدأ البوت:\n"
                    f"   - ID: <code>{user_id}</code>\n"
                    f"   - Username: @{html.escape(user.username or 'غير متوفر')}\n"
                    f"   - الاسم: {html.escape(user.full_name or 'غير معروف')}",
                    context
                )
            except Exception as notify_err:
                 logger.error(f"Failed to send new user notification: {notify_err}")
        else:
            # تحديث نشاط المستخدم الموجود
            update_user_activity(user_id)


            # --- *** رسالة الترحيب ثنائية اللغة *** ---
            first_name_escaped = html.escape(user.first_name or "User")
            bilingual_welcome_msg = f"""
🎉 <b>أهلاً بك في بوت send addons، {first_name_escaped}!</b>
هذا البوت يساعدك على نشر أحدث مودات ماين كرافت تلقائياً في قناتك.

❓ <b>كيف يعمل؟</b>
1️⃣ اختر لغة واجهة البوت أدناه.
2️⃣ اربط قناتك بالبوت (يجب أن يكون البوت مشرفاً فيها مع صلاحية النشر).
3️⃣ حدد الفاصل الزمني الذي ترغب فيه لنشر المودات.
4️⃣ (اختياري) يمكنك تفعيل ميزة معاينة المودات قبل نشرها.

👇 <b>الخطوة الأولى: اختر لغتك المفضلة</b>
(عند اختيار اللغة، سيتم حفظ اختيارك وسيُطلب منك ربط القناة)
---
🎉 <b>Welcome to the send addons bot, {first_name_escaped}!</b>
This bot helps you automatically publish the latest Minecraft mods to your channel.

❓ <b>How does it work?</b>
1️⃣ Choose the bot interface language below.
2️⃣ Link your channel to the bot (the bot must be an admin with posting permissions).
3️⃣ Select the time interval for publishing mods.
4️⃣ (Optional) You can enable the mod preview feature before publishing.

👇 <b>First step: Choose your preferred language</b>
(When you select a language, your choice will be saved, and you'll be prompted to link the channel)
            """
            # --- *** نهاية الرسالة ثنائية اللغة *** ---

            # لا تقم بحفظ لغة افتراضية هنا، انتظر اختيار المستخدم
            # user_channels[user_id_str] = {"lang": "ar"} # <-- تمت إزالتها
            # save_json_file(USER_CHANNELS_FILE, user_channels) # <-- تمت إزالتها

            if update.message:
                 await update.message.reply_text(bilingual_welcome_msg, reply_markup=reply_markup, parse_mode="HTML")
            elif update.callback_query: # حالة غير معتادة للمستخدم الجديد لكن احتياطية
                 is_photo = bool(update.callback_query.message and update.callback_query.message.photo)
                 await update_ui_response(update.callback_query, bilingual_welcome_msg, is_photo=is_photo, reply_markup=reply_markup)
            return # إنهاء المعالجة للمستخدم الجديد هنا

        # --- التعامل مع المستخدم الحالي ---
        user_data = user_channels.get(user_id_str, {})

        # --- المستخدم الحالي لديه قناة مرتبطة ---
        if isinstance(user_data, dict) and user_data.get("channel_id"):
            lang = user_data.get('lang', 'ar') # استخدم اللغة المحفوظة أو العربية
            first_name_escaped = html.escape(user.first_name or "User")
            welcome_back = {
                "ar": f"مرحباً بعودتك، {first_name_escaped}! 👋 يمكنك استخدام /menu للوصول للقائمة.",
                "en": f"Welcome back, {first_name_escaped}! 👋 You can use /menu to access the menu."
            }.get(lang, f"مرحباً بعودتك، {first_name_escaped}! 👋 استخدم /menu.") # الافتراضي عربي

            if update.message:
                await update.message.reply_text(welcome_back)
            elif update.callback_query: # إذا تم استدعاء /start من زر (مثل زر المساعدة)
                 await update.callback_query.answer()
                 await show_main_menu(update, context) # اعرض القائمة الرئيسية
            return

        # --- المستخدم الحالي ليس لديه قناة مرتبطة (لكنه مسجل) ---
        elif isinstance(user_data, dict):
            lang = user_data.get('lang', 'ar') # استخدم اللغة المحفوظة أو العربية
            first_name_escaped = html.escape(user.first_name or "User")
            # استخدم الرسائل أحادية اللغة هنا بناءً على تفضيل المستخدم إن وجد
            welcome_msg_dict = {
                "ar": f"""
🎉 <b>أهلاً بك مجدداً، {first_name_escaped}!</b>
يبدو أنك لم تربط قناة بعد أو أن عملية الربط لم تكتمل.

❓ <b>كيف يعمل؟</b>
1️⃣ اختر لغة واجهة البوت (إذا أردت تغييرها).
2️⃣ اربط قناتك بالبوت.
3️⃣ حدد الفاصل الزمني للنشر.

👇 <b>اختر لغتك أو أكمل ربط القناة</b>
(إذا اخترت لغة، سيتم تحديث الواجهة وسيُطلب منك ربط القناة)
                """,
                "en": f"""
🎉 <b>Welcome back, {first_name_escaped}!</b>
It seems you haven't linked a channel yet or the process wasn't completed.

❓ <b>How does it work?</b>
1️⃣ Choose the bot interface language (if you want to change it).
2️⃣ Link your channel to the bot.
3️⃣ Select the time interval for publishing.

👇 <b>Choose your language or proceed to link your channel</b>
(If you select a language, the interface will update, and you'll be prompted to link the channel)
                """
            }
            welcome_text_for_existing_no_channel = welcome_msg_dict.get(lang, welcome_msg_dict['ar'])

            if update.message:
                 await update.message.reply_text(welcome_text_for_existing_no_channel, reply_markup=reply_markup, parse_mode="HTML")
            elif update.callback_query:
                 is_photo = bool(update.callback_query.message and update.callback_query.message.photo)
                 await update_ui_response(update.callback_query, welcome_text_for_existing_no_channel, is_photo=is_photo, reply_markup=reply_markup)
            return

        # --- حالة غير متوقعة لبيانات المستخدم الحالي ---
        else:
            logger.error(f"Unexpected user data format for existing user {user_id_str}: {user_data}")
            # كحل احتياطي، اعرض الرسالة ثنائية اللغة
            first_name_escaped = html.escape(user.first_name or "User")
            bilingual_fallback_msg = f"""
🎉 <b>أهلاً بك في بوت send addons، {first_name_escaped}!</b>
[... نفس نص الرسالة ثنائية اللغة أعلاه ...]
🎉 <b>Welcome to the send addons bot, {first_name_escaped}!</b>
[... Same bilingual text as above ...]
            """ # اختصارًا، يمكنك لصق النص الكامل هنا
            if update.message:
                 await update.message.reply_text(bilingual_fallback_msg, reply_markup=reply_markup, parse_mode="HTML")
            elif update.callback_query:
                 is_photo = bool(update.callback_query.message and update.callback_query.message.photo)
                 await update_ui_response(update.callback_query, bilingual_fallback_msg, is_photo=is_photo, reply_markup=reply_markup)
            return

    # --- معالجة الأخطاء العامة ---
    except Exception as e:
        logger.error(f"خطأ في أمر /start للمستخدم {update.effective_user.id}: {str(e)}", exc_info=True)
        error_msg = "⚠️ حدث خطأ أثناء معالجة طلبك، يرجى المحاولة لاحقًا."
        try:
            if update.message:
                await update.message.reply_text(error_msg)
            elif update.callback_query:
                await update.callback_query.answer(error_msg, show_alert=True)
        except Exception:
             logger.error(f"Failed to send error message to user {update.effective_user.id} in start handler.")

        # إرسال إشعار للمسؤول بالخطأ
        try:
            await send_notification(
                f"🚨 خطأ في أمر /start:\n"
                f"   - المستخدم: {update.effective_user.id}\n"
                f"   - الخطأ: <pre>{html.escape(str(e))}</pre>",
                context
            )
        except Exception as notify_err:
            logger.error(f"Failed to send error notification for /start: {notify_err}")

async def manage_channel_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles the 'Manage Channel' button press from the main menu."""
    query = update.callback_query
    await query.answer()
    user_id = query.from_user.id
    lang = get_user_lang(user_id)
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id))

    texts = {
        "ar": {"add_change": "➕ إضافة/تغيير القناة", "back": "🔙 العودة للقائمة الرئيسية", "manage_title": "⚙️ إدارة القناة", "current_channel": "القناة الحالية:", "not_linked": "لم تقم بربط قناة بعد."},
        "en": {"add_change": "➕ Add/Change Channel", "back": "🔙 Back to Main Menu", "manage_title": "⚙️ Manage Channel", "current_channel": "Current Channel:", "not_linked": "You haven't linked a channel yet."}
    }
    text = texts.get(lang, texts['ar'])

    keyboard = []
    message_text = f"<b>{text['manage_title']}</b>\n\n"

    if isinstance(user_data, dict) and user_data.get("channel_id"):
        channel_id = user_data["channel_id"]
        # Try to get channel title, fallback to ID
        channel_display_name = f"<code>{channel_id}</code>" # Default to ID
        try:
            # Use a timeout for get_chat to avoid blocking
            chat = await asyncio.wait_for(context.bot.get_chat(channel_id), timeout=5.0)
            channel_display_name = f"{chat.title or 'اسم غير معروف'} (<code>{channel_id}</code>)"
        except asyncio.TimeoutError:
             logger.warning(f"Timeout getting chat info for {channel_id} in manage_channel_menu")
             message_text += "(⚠️ تعذر جلب اسم القناة حالياً)\n"
        except Exception as e:
            logger.warning(f"Could not get chat info for {channel_id} in manage_channel_menu: {e}")
            message_text += "(⚠️ القناة غير متاحة أو تم تغيير معرفها)\n"

        message_text += f"{text['current_channel']} {channel_display_name}\n\n"
        # Button to show detailed settings for the current channel
        keyboard.append([InlineKeyboardButton("🔧 عرض/تعديل الإعدادات", callback_data="show_channel_settings")])
        keyboard.append([InlineKeyboardButton("🔄 تغيير القناة المرتبطة", callback_data="prompt_add_change_channel")]) # Explicit change button
    else:
        # No channel linked
        message_text += text["not_linked"] + "\n\n"
        keyboard.append([InlineKeyboardButton(text["add_change"], callback_data="prompt_add_change_channel")])

    keyboard.append([InlineKeyboardButton(text["back"], callback_data="main_menu")])
    reply_markup = InlineKeyboardMarkup(keyboard)
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message_text, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML") # <-- أضف هذا


async def prompt_add_change_channel(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompts the user to send the channel ID/forward message."""
    query = update.callback_query # Might be called from button
    user_id = query.from_user.id if query else update.effective_user.id
    lang = get_user_lang(user_id) # Get current language

    if query:
        await query.answer()

    setup_texts = {
        "ar": {
            "message": "✍️ <b>لربط قناة جديدة أو تغيير القناة الحالية:</b>\n\n"
                      "1️⃣ أرسل معرف القناة (مثل <code>@my_channel</code> أو <code>-100123456789</code>).\n"
                      "2️⃣ أو قم بإعادة توجيه (Forward) أي رسالة من القناة المطلوبة إلى هنا.\n\n"
                      "⚠️ <b>هام:</b> يجب أن يكون البوت <b>مشرفاً</b> في القناة ولديه صلاحية <b>نشر الرسائل</b>.",
             "back": "🔙 إلغاء والعودة"
        },
        "en": {
            "message": "✍️ <b>To link a new channel or change the current one:</b>\n\n"
                      "1️⃣ Send the channel ID (e.g., <code>@my_channel</code> or <code>-100123456789</code>).\n"
                      "2️⃣ Or forward any message from the desired channel here.\n\n"
                      "⚠️ <b>Important:</b> The bot must be an <b>admin</b> in the channel with permission to <b>post messages</b>.",
             "back": "🔙 Cancel and Go Back"
        }
    }
    text = setup_texts.get(lang, setup_texts['ar'])
    message_text = text["message"]
    # Determine where 'Back' should go
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id))
    back_callback = "manage_channel_menu" if isinstance(user_data, dict) and user_data.get("channel_id") else "main_menu"

    back_button = InlineKeyboardButton(text["back"], callback_data=back_callback)
    reply_markup = InlineKeyboardMarkup([[back_button]])


    if query:
        is_photo = bool(query.message and query.message.photo)
        await update_ui_response(query, message_text, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML") # <-- أضف هذا
    elif update.message: # Should not happen if called from button, but as fallback
        await update.message.reply_text(message_text, reply_markup=reply_markup, parse_mode="HTML")

    # Set state for the message handler
    context.user_data["waiting_for_channel"] = True
    logger.info(f"User {user_id} is now waiting for channel input.")


async def handle_channel_input(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles user input when expecting a channel ID, forward, or t.me/ link."""
    user = update.effective_user
    user_id = user.id

    # التحقق من حالة الانتظار أولاً
    if not context.user_data.get("waiting_for_channel", False):
        logger.debug(f"handle_channel_input: Ignoring message from user {user_id} as not waiting for channel.")
        if update.message and update.message.text and update.message.text.startswith('/'):
            pass # اترك معالجات الأوامر تتعامل معها
        return

    logger.info(f"handle_channel_input: Processing input from user {user_id} (waiting_for_channel=True)")
    lang = get_user_lang(user_id)
    user_id_str = str(user_id)
    message = update.message # الرسالة التي أرسلها المستخدم

    # --- النصوص المحدثة ---
    texts = {
        "ar": {
            "invalid": "لم أتعرف على القناة. يرجى إرسال معرف صحيح (مثل <code>@username</code> أو <code>-100...</code> أو رابط <code>https://t.me/username</code>) أو إعادة توجيه رسالة من القناة.",
            "not_found": "لم أتمكن من العثور على القناة <code>{}</code>. تأكد من صحة المعرف/الرابط.",
            "not_channel": "المعرف/الرابط <code>{}</code> لا يشير إلى قناة.",
            "no_permission": "⚠️ فشل التحقق. يبدو أن البوت ليس لديه الصلاحيات المطلوبة في القناة <code>{}</code>.\n\n"
                           "✅ **الحل:**\n"
                           "1. تأكد من إضافة البوت كـ**مشرف (Admin)** في القناة.\n"
                           "2. تأكد من منحه صلاحية **'نشر الرسائل' (Post Messages)**.\n"
                           "3. أرسل معرف/رابط القناة مرة أخرى.",
            "error": "⚠️ فشل التحقق من القناة <code>{}</code>.\n\n"
                     "الرجاء اضافة بوت كمشرف في قناتك:\n"
                     "- البوت ليس عضواً في القناة.\n"
                     "- البوت ليس مشرفاً أو لا يملك صلاحية 'نشر الرسائل'.\n"
                     "- الرجاء ربط قناتك مع البوت.\n\n"
                     "👉 يرجي اضافة بوت في قناتك كمشرف واعطائه صلاحيات ارسال رسائل  ثم إعادة إرسال اليوزر مرة اخرى/اتبع تعليمات.",
            "found": "✅ تم العثور على القناة: <b>{}</b>\n\nالرجاء اختيار الفاصل الزمني المطلوب لنشر المودات:",
            "buttons": {
                "1m": "كل دقيقة",
                "30m": "كل نصف ساعة",
                "1h": "كل ساعة",
                "90m": "كل ساعة ونصف",
                "2h": "كل ساعتين",
                "3h": "كل 3 ساعات",
                "4h": "كل 4 ساعات",
                "5h": "كل 5 ساعات",
                "6h": "كل 6 ساعات",
                "8h": "كل 8 ساعات",
                "9h": "كل 9 ساعات",
                "10h": "كل 10 ساعات",
                "12h": "كل 12 ساعة",
                "18h": "كل 18 ساعة",
                "24h": "كل 24 ساعة"
            },
            "checking": "⏳ جارٍ التحقق من القناة...",
            "duplicate_channel": "⚠️ عذراً، هذه القناة <code>{}</code> مرتبطة بالفعل بحساب مستخدم آخر ({}) في البوت. لا يمكن ربطها مرة أخرى."
        },
        "en": { # Add English versions if needed
            "invalid": "Could not recognize the channel. Please send a valid ID (e.g., <code>@username</code>, <code>-100...</code>, or <code>https://t.me/username</code> link) or forward a message.",
            "not_found": "Could not find the channel <code>{}</code>. Make sure the ID/link is correct.",
            "not_channel": "The ID/link <code>{}</code> does not point to a channel.",
            "no_permission": "⚠️ Verification failed. The bot might not have the required permissions in channel <code>{}</code>.\n\n"
                           "✅ **Solution:**\n"
                           "1. Ensure the bot is added as an **Admin** in the channel.\n"
                           "2. Ensure it has the **'Post Messages'** permission.\n"
                           "3. Send the channel ID/link again.",
            "error": "⚠️ Failed to verify channel <code>{}</code>.\n\n"
                     "Possible reasons:\n"
                     "- The bot is not a member of the channel.\n"
                     "- The bot is not an admin or lacks 'Post Messages' permission.\n"
                     "- The ID/link is incorrect.\n\n"
                     "👉 Please check these points and then resend the ID/link.",
            "found": "✅ Channel found: <b>{}</b>\n\nPlease select the desired publishing interval for mods:",
            "buttons": {
                "1m": "Every minute",
                "30m": "Every 30 minutes",
                "1h": "Hourly",
                "90m": "Every 90 minutes",
                "2h": "Every 2 hours",
                "3h": "Every 3 hours",
                "4h": "Every 4 hours",
                "5h": "Every 5 hours",
                "6h": "Every 6 hours",
                "8h": "Every 8 hours",
                "9h": "Every 9 hours",
                "10h": "Every 10 hours",
                "12h": "Every 12 hours",
                "18h": "Every 18 hours",
                "24h": "Every 24 hours"
            },
            "checking": "⏳ Checking channel...",
            "duplicate_channel": "⚠️ Sorry, this channel <code>{}</code> is already linked to another user ({}) in the bot. It cannot be linked again."
        }
    }
    text = texts.get(lang, texts['ar'])

    channel_id_input = None
    input_source_type = "Unknown"
    checking_msg = None # لرسالة "جارٍ التحقق"
    validated_channel_id = None # لتخزين المعرف النهائي بعد التحقق
    chat_title = None # لتخزين عنوان القناة

    if not message:
        logger.warning(f"handle_channel_input called for user {user_id} without update.message.")
        return

    # --- استخراج معرف القناة من رسالة المستخدم ---
    forward_chat = getattr(message, 'forward_from_chat', None)
    if forward_chat and forward_chat.type == telegram.constants.ChatType.CHANNEL: # استخدام الثابت الصحيح
        channel_id_input = str(forward_chat.id)
        input_source_type = "Forward"
        logger.info(f"Channel ID {channel_id_input} received via forward from user {user_id}")
    elif message.text:
        input_text = message.text.strip()
        if 't.me/' in input_text:
            try:
                parts = input_text.split('t.me/')
                if len(parts) > 1 and parts[1]:
                    potential_username = parts[1].split('/')[0].split('?')[0]
                    if potential_username:
                        channel_id_input = f"@{potential_username}"
                        input_source_type = "t.me Link"
                        logger.info(f"Potential channel username '{potential_username}' from link by user {user_id}. Treating as '{channel_id_input}'.")
            except Exception as e: logger.warning(f"Error parsing t.me link '{input_text}' from user {user_id}: {e}")
        if channel_id_input is None: # إذا لم يكن رابطاً، تحقق من الصيغ الأخرى
            if input_text.startswith("@") and len(input_text) > 1:
                channel_id_input = input_text
                input_source_type = "@username Text"
                logger.info(f"Channel Username '{channel_id_input}' received via text from user {user_id}")
            elif input_text.startswith("-100") and input_text[1:].isdigit() and len(input_text) > 5:
                channel_id_input = input_text
                input_source_type = "Private ID Text"
                logger.info(f"Channel Private ID '{channel_id_input}' received via text from user {user_id}")

    # --- التعامل مع الإدخال غير الصالح ---
    if channel_id_input is None:
         cancel_button = InlineKeyboardButton("❌ إلغاء", callback_data="manage_channel_menu" if load_user_channels().get(user_id_str, {}).get("channel_id") else "main_menu")
         await message.reply_text(text["invalid"], parse_mode="HTML", reply_markup=InlineKeyboardMarkup([[cancel_button]]))
         context.user_data["waiting_for_channel"] = True # اطلب مرة أخرى
         return

    # --- بدء عملية التحقق ---
    try:
        # إرسال رسالة "جارٍ التحقق"
        try:
            checking_msg = await message.reply_text(text["checking"], parse_mode="HTML")
        except Exception as reply_err:
            logger.error(f"Failed to send 'checking' message to user {user_id}: {reply_err}")
            # تابع حتى لو فشلت هذه الرسالة

        target_chat_id_for_check = None

        # --- جلب ID القناة الفعلي إذا كان الإدخال username ---
        if isinstance(channel_id_input, str) and channel_id_input.startswith('@'):
            try:
                chat_info = await context.bot.get_chat(channel_id_input)
                target_chat_id_for_check = str(chat_info.id)
                chat_title = chat_info.title # حفظ العنوان الأولي
                logger.info(f"Resolved '{channel_id_input}' to channel ID {target_chat_id_for_check} ('{chat_title}') for user {user_id}.")
            except (BadRequest, Forbidden, TelegramError) as e:
                logger.warning(f"Failed to resolve username '{channel_id_input}' (User: {user_id}): {e}")
                error_key = "not_found" if "chat not found" in str(e).lower() else "error" # استخدام الخطأ العام إذا لم يكن not_found
                error_message = text[error_key].format(html.escape(channel_id_input))
                if checking_msg: await checking_msg.edit_text(error_message, parse_mode="HTML")
                else: await message.reply_text(error_message, parse_mode="HTML")
                context.user_data["waiting_for_channel"] = True
                return
        else:
            # الإدخال هو ID رقمي بالفعل
            target_chat_id_for_check = str(channel_id_input)
            logger.info(f"Using provided numeric ID '{target_chat_id_for_check}' for validation (User: {user_id}).")

        # --- التحقق من التكرار ---
        user_channels_data = load_user_channels()
        for existing_user_id, existing_user_data in user_channels_data.items():
             if isinstance(existing_user_data, dict) and existing_user_data.get("channel_id") == target_chat_id_for_check:
                 if existing_user_id != user_id_str:
                     other_user_display = f"<code>{existing_user_id}</code>"
                     error_msg = text["duplicate_channel"].format(html.escape(target_chat_id_for_check), other_user_display)
                     if checking_msg: await checking_msg.edit_text(error_msg, parse_mode="HTML")
                     else: await message.reply_text(error_msg, parse_mode="HTML")
                     context.user_data["waiting_for_channel"] = True
                     return
                        # لا نوقف المعالجة هنا، نسمح له بتحديث الفاصل الزمني
            # --- نهاية التحقق من التكرار ---

            # Simplified permission check function (as before)
        async def check_perms_and_get_title(bot: telegram.Bot, chan_id: str, usr_id: str) -> tuple[bool, str | None, str | None]:
            """Checks bot permissions and gets channel title. Returns (success, title, error_code)."""
            try:
                # استخدام get_chat أولاً للتأكد من وجود القناة والحصول على العنوان
                chat = await bot.get_chat(chan_id)
                # استخدام الثابت الصحيح للتحقق من النوع
                if chat.type != telegram.constants.ChatType.CHANNEL:
                    logger.warning(f"Check perms: ID {chan_id} is not a channel (Type: {chat.type})")
                    return False, chat.title or f"ID:{chan_id}", "not_channel"

                # الآن تحقق من العضوية والصلاحيات
                member = await bot.get_chat_member(chan_id, bot.id)
                if isinstance(member, (ChatMemberAdministrator, ChatMemberOwner)) and member.can_post_messages:
                    logger.debug(f"Check perms OK for {chan_id}. Bot is Admin/Owner with post rights.")
                    return True, chat.title or f"ID:{chan_id}", None # نجح
                else:
                    logger.warning(f"Check perms failed for {chan_id}. Bot status: {member.status}, Can post: {getattr(member, 'can_post_messages', 'N/A')}")
                    return False, chat.title or f"ID:{chan_id}", "no_permission" # فشل الصلاحيات
            except Forbidden as e_inner: # خطأ صلاحيات عند محاولة جلب المعلومات
                logger.warning(f"Check perms Forbidden error for {chan_id} (User: {usr_id}): {e_inner}")
                # نعتبر هذا بمثابة "لا يوجد صلاحية" أو "ليس عضواً"
                # يمكن تمييز "not member" إذا كان النص واضحاً في الاستثناء
                if "bot is not a member" in str(e_inner).lower():
                    return False, f"ID:{chan_id}", "not_member_forbidden"
                else:
                    return False, f"ID:{chan_id}", "forbidden_generic" # خطأ صلاحية عام
            except BadRequest as e_inner: # أخطاء أخرى مثل عدم العثور
                err_str = str(e_inner).lower()
                logger.warning(f"Check perms BadRequest error for {chan_id} (User: {usr_id}): {e_inner}")
                if "chat not found" in err_str or "peer_id_invalid" in err_str:
                    return False, f"ID:{chan_id}", "not_found_api"
                else:
                    return False, f"ID:{chan_id}", "error_api" # خطأ API عام
            except TelegramError as e_inner: # أخطاء تيليجرام أخرى (شبكة، مهلة، إلخ)
                logger.warning(f"Check perms TelegramError for {chan_id} (User: {usr_id}): {e_inner}")
                return False, f"ID:{chan_id}", "error_telegram" # رمز خطأ تيليجرام عام
            except Exception as e_inner: # أي خطأ آخر غير متوقع
                logger.error(f"Unexpected error inside check_perms_and_get_title for {chan_id} (User: {usr_id}): {e_inner}", exc_info=True)
                return False, f"ID:{chan_id}", "error_unexpected"
        # --- نهاية الدالة المضمنة ---

        # --- استدعاء دالة التحقق ومعالجة النتيجة ---
        permissions_ok, fetched_title, error_code = await check_perms_and_get_title(context.bot, target_chat_id_for_check, user_id_str)
        chat_title = fetched_title or chat_title or target_chat_id_for_check

        if permissions_ok:
            validated_channel_id = target_chat_id_for_check # تم التحقق بنجاح
            logger.info(f"Channel {validated_channel_id} ('{chat_title}') validated successfully for user {user_id}.")
        else:
            # معالجة الأخطاء بناءً على error_code باستخدام النصوص المحدثة
            error_key_map = {
                "not_channel": "not_channel",
                "no_permission": "no_permission",
                "not_member_forbidden": "no_permission", # عاملها كخطأ صلاحيات
                "forbidden_generic": "no_permission", # عاملها كخطأ صلاحيات
                "not_found_api": "not_found",
                "error_api": "error", # خطأ API -> رسالة الخطأ العامة الجديدة
                "error_telegram": "error", # خطأ Telegram -> رسالة الخطأ العامة الجديدة
                "error_unexpected": "error" # خطأ غير متوقع -> رسالة الخطأ العامة الجديدة
            }
            error_key = error_key_map.get(error_code, "error")
            channel_display_name_on_error = chat_title if chat_title != target_chat_id_for_check else (channel_id_input or f"ID:{target_chat_id_for_check}")
            error_message = text[error_key].format(html.escape(str(channel_display_name_on_error)))

            if checking_msg: await checking_msg.edit_text(error_message, parse_mode="HTML")
            else: await message.reply_text(error_message, parse_mode="HTML")
            context.user_data["waiting_for_channel"] = True
            logger.info(f"Channel validation failed for user {user_id} with code '{error_code}'. Prompting user again.")
            return

    # --- معالجة الأخطاء الحرجة غير المتوقعة أثناء العملية بأكملها ---
    except Exception as e:
        logger.critical(f"Critical error validating channel input '{channel_id_input}' for user {user_id}: {e}", exc_info=True)
        channel_display_name_on_crit_error = chat_title or channel_id_input or 'القناة/ID'
        error_msg = text.get("error", "An unexpected error occurred.").format(html.escape(str(channel_display_name_on_crit_error)))

        if checking_msg:
            try: await checking_msg.edit_text(error_msg, parse_mode="HTML")
            except Exception: pass
        else:
            try: await message.reply_text(error_msg, parse_mode="HTML")
            except Exception: pass

        try: await send_notification(f"🚨 Critical error during channel validation:\nUser: {user_id}\nInput: {channel_id_input}\nError: <pre>{html.escape(str(e))}</pre>", context)
        except Exception: pass
        context.user_data["waiting_for_channel"] = True
        return

    # --- المتابعة إذا كان التحقق ناجحًا (validated_channel_id تم تعيينه) ---
    if validated_channel_id:
        context.user_data["pending_channel_id"] = validated_channel_id
        context.user_data["pending_channel_title"] = chat_title
        logger.info(f"Storing pending channel {validated_channel_id} ('{chat_title}') for user {user_id}.")

        context.user_data["waiting_for_channel"] = False # مسح حالة الانتظار
        logger.info(f"Cleared waiting_for_channel state for user {user_id}")

        # --- بناء أزرار الفاصل الزمني ---
        interval_buttons_texts = text["buttons"]
        keyboard = [
            [
                InlineKeyboardButton(interval_buttons_texts["1m"], callback_data="interval_min_1"),
                InlineKeyboardButton(interval_buttons_texts["30m"], callback_data="interval_min_30"),
                InlineKeyboardButton(interval_buttons_texts["1h"], callback_data="interval_min_60"),
            ],
            [
                InlineKeyboardButton(interval_buttons_texts["90m"], callback_data="interval_min_90"),
                InlineKeyboardButton(interval_buttons_texts["2h"], callback_data="interval_min_120"),
                InlineKeyboardButton(interval_buttons_texts["3h"], callback_data="interval_min_180"),
                InlineKeyboardButton(interval_buttons_texts["4h"], callback_data="interval_min_240"),
            ],
            [
                InlineKeyboardButton(interval_buttons_texts["5h"], callback_data="interval_min_300"),
                InlineKeyboardButton(interval_buttons_texts["6h"], callback_data="interval_min_360"),
                InlineKeyboardButton(interval_buttons_texts["8h"], callback_data="interval_min_480"),
            ],
            [
                InlineKeyboardButton(interval_buttons_texts["9h"], callback_data="interval_min_540"),
                InlineKeyboardButton(interval_buttons_texts["10h"], callback_data="interval_min_600"),
                InlineKeyboardButton(interval_buttons_texts["12h"], callback_data="interval_min_720"),
            ],
            [
                InlineKeyboardButton(interval_buttons_texts["18h"], callback_data="interval_min_1080"),
                InlineKeyboardButton(interval_buttons_texts["24h"], callback_data="interval_min_1440"),
            ]
        ]
        # ----------------------------------------

        logger.info(f"Sending interval selection message to user {user_id}")
        # --- تحديث رسالة "جارٍ التحقق" أو إرسال رسالة جديدة ---
        success_msg = text["found"].format(html.escape(chat_title or validated_channel_id))
        final_markup = InlineKeyboardMarkup(keyboard)
        try:
            if checking_msg:
                await checking_msg.edit_text(
                    success_msg,
                    reply_markup=final_markup,
                    parse_mode="HTML"
                )
            else: # إذا فشل إرسال رسالة التحقق الأولى
                 await message.reply_text(
                    success_msg,
                    reply_markup=final_markup,
                    parse_mode="HTML"
                 )
            logger.info(f"Interval selection message sent/edited successfully for user {user_id}")
        except Exception as final_send_err:
             logger.error(f"Failed to send/edit the final interval selection message for user {user_id}: {final_send_err}")
             try:
                 final_error_msg = text.get("error", "An error occurred.").format("the channel")
                 await message.reply_text(final_error_msg)
             except: pass
             # إعادة المستخدم لحالة الانتظار لأن الخطوة فشلت
             context.user_data["waiting_for_channel"] = True
             context.user_data.pop("pending_channel_id", None)
             context.user_data.pop("pending_channel_title", None)

    # هذا الجزء يجب ألا يتم الوصول إليه إذا كانت المعالجة صحيحة
    else:
        logger.error(f"Reached end of handle_channel_input for user {user_id} without validated_channel_id being set. Input was '{channel_id_input}'. This indicates a logic flaw.")
        fallback_error_msg = text.get("error", "An unexpected error occurred.").format(channel_id_input or "the channel")
        if checking_msg:
            try: await checking_msg.edit_text(fallback_error_msg, parse_mode="HTML")
            except: await message.reply_text(fallback_error_msg, parse_mode="HTML")
        else:
            await message.reply_text(fallback_error_msg, parse_mode="HTML")
        context.user_data["waiting_for_channel"] = True

async def handle_interval_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handles interval selection after channel validation, saves settings,
    and displays the final welcome message with menu button.
    """
    try:
        query = update.callback_query
        await query.answer()

        if not query.data.startswith("interval_min_"):
            logger.warning(f"Invalid callback data in interval selection: {query.data}")
            return

        interval_minutes = int(query.data.split("_")[2]) # Get minutes from callback
        user_id = query.from_user.id
        lang = get_user_lang(user_id)

        channel_id = context.user_data.get("pending_channel_id")
        channel_title = context.user_data.get("pending_channel_title", "القناة المحددة")

        if not channel_id:
            error_texts_no_channel = {
                "ar": "❌ لم يتم العثور على بيانات القناة المؤقتة. يرجى إعادة محاولة ربط القناة من البداية.",
                "en": "❌ Temporary channel data not found. Please try linking the channel again from the beginning."
            }
            error_msg_no_channel = error_texts_no_channel.get(lang, error_texts_no_channel['ar'])
            logger.error(f"Error linking channel for user {user_id}: pending_channel_id not found in user_data.")
            # --- تعديل: استخدام update_ui_response ---
            is_photo_err = bool(query.message and query.message.photo)
            await update_ui_response(query, error_msg_no_channel, is_photo=is_photo_err)
            # --------------------------------------
            await send_notification(f"⚠️ خطأ في ربط القناة للمستخدم {user_id}: لم يتم العثور على pending_channel_id", context)
            context.user_data.pop("pending_channel_id", None)
            context.user_data.pop("pending_channel_title", None)
            context.user_data.pop("waiting_for_channel", None)
            return

        # Save user channel with language and interval in minutes (but don't clear context yet)
        save_user_channel(user_id, channel_id, interval_minutes, lang)

        # Store interval in context for the next step
        context.user_data["pending_interval"] = interval_minutes

        # --- تعديل: رسالة الترحيب الجديدة ---
        welcome_texts = {
            "ar": {
                "title": "🎉 أهلاً بك في بوت send addons!",
                "body": "تم ربط قناتك ({}) بنجاح وضبط الفاصل الزمني للنشر على: {}.\n\n"
                        "📝 <b>كيف يعمل البوت؟</b>\n"
                        "- سيقوم البوت الآن تلقائياً باقتراح مودات جديدة لك للموافقة عليها (إذا كانت المعاينة مفعلة) أو بنشرها مباشرة في قناتك حسب الفاصل الزمني المحدد.\n"
                        "- يمكنك دائماً التحكم في إعدادات القناة (الفاصل، تفعيل/إيقاف النشر، تفعيل/إيقاف المعاينة) من القائمة الرئيسية.\n\n"
                        "اضغط على الزر أدناه للوصول إلى القائمة الرئيسية أو استخدم الأمر /menu.",
                "menu_button": "🏠 القائمة الرئيسية"
            },
            "en": {
                "title": "🎉 Welcome to the send addons Bot!",
                "body": "Your channel ({}) has been successfully linked and the publishing interval set to: {}.\n\n"
                        "📝 <b>How does it work?</b>\n"
                        "- The bot will now automatically suggest new mods for your approval (if preview is enabled) or publish them directly to your channel based on the set interval.\n"
                        "- You can always manage your channel settings (interval, toggle publishing, toggle preview) from the main menu.\n\n"
                        "Press the button below to access the main menu or use the /menu command.",
                "menu_button": "🏠 Main Menu"
            }
        }
        welcome_text_content = welcome_texts.get(lang, welcome_texts['ar'])

        # --- تعديل: تنسيق عرض الفاصل الزمني ليشمل الخيارات الجديدة ---
        if interval_minutes == 1:
            interval_display_text = "كل دقيقة" if lang == "ar" else "Every minute"
        elif interval_minutes == 30:
            interval_display_text = "كل نصف ساعة" if lang == "ar" else "Every 30 minutes"
        elif interval_minutes == 60:
            interval_display_text = "كل ساعة واحدة" if lang == "ar" else "Every 1 hour"
        elif interval_minutes == 90:
            interval_display_text = "كل ساعة ونصف" if lang == "ar" else "Every 90 minutes"
        elif interval_minutes == 120:
            interval_display_text = "كل ساعتين" if lang == "ar" else "Every 2 hours"
        elif interval_minutes == 180:
            interval_display_text = "كل 3 ساعات" if lang == "ar" else "Every 3 hours"
        elif interval_minutes == 240:
            interval_display_text = "كل 4 ساعات" if lang == "ar" else "Every 4 hours"
        elif interval_minutes == 300:
            interval_display_text = "كل 5 ساعات" if lang == "ar" else "Every 5 hours"
        elif interval_minutes == 360:
            interval_display_text = "كل 6 ساعات" if lang == "ar" else "Every 6 hours"
        elif interval_minutes == 480:
            interval_display_text = "كل 8 ساعات" if lang == "ar" else "Every 8 hours"
        elif interval_minutes == 540:
            interval_display_text = "كل 9 ساعات" if lang == "ar" else "Every 9 hours"
        elif interval_minutes == 600:
            interval_display_text = "كل 10 ساعات" if lang == "ar" else "Every 10 hours"
        elif interval_minutes == 720:
            interval_display_text = "كل 12 ساعة" if lang == "ar" else "Every 12 hours"
        elif interval_minutes == 1080:
            interval_display_text = "كل 18 ساعة" if lang == "ar" else "Every 18 hours"
        elif interval_minutes == 1440:
            interval_display_text = "كل 24 ساعة" if lang == "ar" else "Every 24 hours"
        elif interval_minutes % 60 == 0:
             hours = interval_minutes // 60
             interval_display_text = f"كل {hours} ساعات" if lang == "ar" else f"Every {hours} hours"
        else: # Default fallback (shouldn't happen with buttons but good for command)
            interval_display_text = f"كل {interval_minutes} دقيقة" if lang == "ar" else f"Every {interval_minutes} minutes"
        # --- نهاية تعديل التنسيق ---

        # --- عرض قائمة اختيار نوع المودات ---
        await show_mod_categories_selection(update, context)

    except Exception as e:
        logger.critical(f"Critical error in handle_interval_selection for user {update.effective_user.id}: {str(e)}", exc_info=True)
        error_texts_saving = {
            "ar": {"error_saving": "❌ حدث خطأ أثناء حفظ الإعدادات أو عرض رسالة الترحيب. يرجى المحاولة مرة أخرى لاحقاً."},
            "en": {"error_saving": "❌ An error occurred while saving settings or showing the welcome message. Please try again later."}
        }
        lang_err = get_user_lang(update.effective_user.id)
        error_msg_saving = error_texts_saving.get(lang_err, error_texts_saving['ar'])["error_saving"]
        if query:
            is_photo_err = bool(query.message and query.message.photo)
            await update_ui_response(query, error_msg_saving, is_photo=is_photo_err)
        await send_notification(
            f"🚨 خطأ فادح في handle_interval_selection:\n"
            f"   - المستخدم: {update.effective_user.id}\n"
            f"   - الخطأ: <pre>{html.escape(str(e))}</pre>",
            context
        )
        context.user_data.pop("pending_channel_id", None)
        context.user_data.pop("pending_channel_title", None)
        context.user_data.pop("waiting_for_channel", None)


async def show_mod_categories_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة اختيار نوع المودات التي يريد المستخدم نشرها"""
    query = update.callback_query
    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # النصوص
    category_texts = {
        "ar": {
            "title": "🎯 <b>اختيار نوع المحتوى</b>",
            "description": "اختر نوع المحتوى الذي تريد نشره في قناتك:\n\n📌 يمكنك اختيار تصنيف واحد أو عدة تصنيفات أو جميعها.",
            "categories": {
                "addons": "🧩 إضافات (Add-ons)",
                "shaders": "✨ شيدرات (Shaders)",
                "texture_packs": "🎨 حزم النسيج (Texture Packs)",
                "seeds": "🌱 البذور (Seeds)",
                "maps": "🗺️ الخرائط (Maps)"
            },
            "select_all": "✅ تحديد الكل",
            "continue": "➡️ متابعة",
            "back": "🔙 العودة لاختيار الفترة"
        },
        "en": {
            "title": "🎯 <b>Content Type Selection</b>",
            "description": "Choose the type of content you want to publish to your channel:\n\n📌 You can select one category, multiple categories, or all of them.",
            "categories": {
                "addons": "🧩 Add-ons",
                "shaders": "✨ Shaders",
                "texture_packs": "🎨 Texture Packs",
                "seeds": "🌱 Seeds",
                "maps": "🗺️ Maps"
            },
            "select_all": "✅ Select All",
            "continue": "➡️ Continue",
            "back": "🔙 Back to Interval Selection"
        }
    }

    text = category_texts.get(lang, category_texts['ar'])

    # الحصول على التصنيفات المختارة حالياً (افتراضياً جميعها)
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_categories = user_data.get('mod_categories', ['addons', 'shaders', 'texture_packs', 'seeds', 'maps'])

    # بناء الرسالة
    message = f"{text['title']}\n\n{text['description']}\n\n"

    # بناء الأزرار
    keyboard = []

    # أزرار التصنيفات
    for category_key, category_name in text['categories'].items():
        is_selected = category_key in selected_categories
        button_text = f"{'✅' if is_selected else '☐'} {category_name}"
        keyboard.append([InlineKeyboardButton(button_text, callback_data=f"toggle_category_{category_key}")])

    # أزرار التحكم
    keyboard.append([InlineKeyboardButton(text["select_all"], callback_data="select_all_categories")])
    keyboard.append([InlineKeyboardButton(text["continue"], callback_data="finish_mod_categories_selection")])
    keyboard.append([InlineKeyboardButton(text["back"], callback_data="back_to_interval_selection")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    # عرض الرسالة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def toggle_mod_category(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تبديل تحديد تصنيف معين"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # استخراج التصنيف من callback_data
    category = query.data.split("_")[-1]  # toggle_category_addons -> addons

    # تحديث التصنيفات
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    selected_categories = user_data.get('mod_categories', ['addons', 'shaders', 'texture_packs', 'seeds', 'maps'])

    if category in selected_categories:
        selected_categories.remove(category)
    else:
        selected_categories.append(category)

    # حفظ التحديث
    user_data['mod_categories'] = selected_categories
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await show_mod_categories_selection(update, context)


async def select_all_categories(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تحديد جميع التصنيفات"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # تحديد جميع التصنيفات
    all_categories = ['addons', 'shaders', 'texture_packs', 'seeds', 'maps']

    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    user_data['mod_categories'] = all_categories
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await show_mod_categories_selection(update, context)


async def finish_mod_categories_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """إنهاء اختيار التصنيفات والانتقال لاختيار إصدارات Minecraft"""
    query = update.callback_query
    await query.answer()

    # الانتقال لاختيار إصدارات Minecraft
    await show_mc_versions_selection(update, context)


# --- دوال اختيار إصدارات Minecraft ---

async def show_mc_versions_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة اختيار إصدارات Minecraft"""
    query = update.callback_query
    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # الحصول على الإصدارات المختارة حالياً
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_versions = user_data.get('mc_versions', ['1.21+', '1.20+', '1.19+', '1.18+'])

    # النصوص
    texts = {
        "ar": {
            "title": "🎮 <b>اختر إصدارات Minecraft:</b>",
            "description": "حدد إصدارات Minecraft التي تريد نشر مودات لها في قناتك:",
            "select_all": "✅ تحديد الكل",
            "continue": "➡️ متابعة",
            "back": "🔙 العودة"
        },
        "en": {
            "title": "🎮 <b>Select Minecraft Versions:</b>",
            "description": "Choose the Minecraft versions you want to publish mods for in your channel:",
            "select_all": "✅ Select All",
            "continue": "➡️ Continue",
            "back": "🔙 Back"
        }
    }
    text = texts.get(lang, texts['ar'])

    # قائمة الإصدارات المتاحة
    available_versions = ['1.21+', '1.20+', '1.19+', '1.18+']

    # إنشاء الأزرار
    keyboard = []

    # أزرار الإصدارات (صفين)
    row1 = []
    row2 = []
    for i, version in enumerate(available_versions):
        is_selected = version in selected_versions
        button_text = f"{'✅' if is_selected else '☐'} {version}"
        callback_data = f"toggle_version_{version}"

        if i < 2:  # أول صفين
            row1.append(InlineKeyboardButton(button_text, callback_data=callback_data))
        else:  # باقي الأزرار
            row2.append(InlineKeyboardButton(button_text, callback_data=callback_data))

    if row1:
        keyboard.append(row1)
    if row2:
        keyboard.append(row2)

    # زر تحديد الكل
    keyboard.append([InlineKeyboardButton(text["select_all"], callback_data="select_all_versions")])

    # أزرار التنقل
    keyboard.append([
        InlineKeyboardButton(text["back"], callback_data="back_to_categories"),
        InlineKeyboardButton(text["continue"], callback_data="finish_versions_selection")
    ])

    # تكوين الرسالة
    message = f"{text['title']}\n\n{text['description']}"

    reply_markup = InlineKeyboardMarkup(keyboard)

    # عرض الرسالة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def toggle_mc_version(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تبديل تحديد إصدار معين"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # استخراج الإصدار من callback_data
    version = query.data.split("_")[-1]  # toggle_version_1.21+ -> 1.21+

    # تحديث الإصدارات
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    selected_versions = user_data.get('mc_versions', ['1.21+', '1.20+', '1.19+', '1.18+'])

    if version in selected_versions:
        selected_versions.remove(version)
    else:
        selected_versions.append(version)

    # حفظ التحديث
    user_data['mc_versions'] = selected_versions
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await show_mc_versions_selection(update, context)


async def select_all_mc_versions(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تحديد جميع إصدارات Minecraft"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # تحديد جميع الإصدارات
    all_versions = ['1.21+', '1.20+', '1.19+', '1.18+']

    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    user_data['mc_versions'] = all_versions
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await show_mc_versions_selection(update, context)


async def back_to_categories_from_versions(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """العودة لاختيار التصنيفات من اختيار الإصدارات"""
    query = update.callback_query
    await query.answer()

    # العودة لعرض قائمة التصنيفات
    await show_mod_categories_selection(update, context)


async def finish_mc_versions_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """إنهاء اختيار إصدارات Minecraft والانتقال لاختيار تنسيق الرسالة"""
    query = update.callback_query
    await query.answer()

    # الانتقال لاختيار تنسيق الرسالة
    await show_message_format_selection(update, context)


# --- دوال تنسيق الرسائل ---

def format_mod_message(mod, format_type, lang="ar"):
    """تنسيق رسالة المود حسب النوع المختار"""

    # استخراج البيانات
    name = mod.get('name', 'مود غير معروف')
    description = mod.get('description', 'لا يوجد وصف متاح')
    mc_version = mod.get('mc_version', 'غير محدد')
    download_link = mod.get('download_link', '')
    category = mod.get('category', 'addons')

    # تحديد نوع المود
    category_names = {
        "ar": {
            "addons": "مـود",
            "shaders": "شيدر",
            "texture_packs": "حزمة نسيج",
            "seeds": "بذرة",
            "maps": "خريطة"
        },
        "en": {
            "addons": "Mod",
            "shaders": "Shader",
            "texture_packs": "Texture Pack",
            "seeds": "Seed",
            "maps": "Map"
        }
    }

    category_name = category_names.get(lang, category_names['ar']).get(category, "مـود")

    if format_type == "classic":
        return format_classic_message(name, description, mc_version, download_link, category_name, lang)
    elif format_type == "modern":
        return format_modern_message(name, description, mc_version, download_link, category_name, lang)
    elif format_type == "elegant":
        return format_elegant_message(name, description, mc_version, download_link, category_name, lang)
    elif format_type == "minimal":
        return format_minimal_message(name, description, mc_version, download_link, category_name, lang)
    elif format_type == "gaming":
        return format_gaming_message(name, description, mc_version, download_link, category_name, lang)
    else:
        return format_classic_message(name, description, mc_version, download_link, category_name, lang)


def format_classic_message(name, description, mc_version, download_link, category_name, lang):
    """التنسيق الكلاسيكي"""
    if lang == "ar":
        message = f"#{category_name}\n\n"
        message += f"📝 <b>الاسم:</b> {name}\n\n"
        message += f"📋 <b>الوصف:</b>\n{description}\n\n"
        message += f"🎮 <b>الإصدار:</b> {mc_version}\n\n"
        message += f"📥 <b>رابط التحميل:</b>\n{download_link}"
    else:
        message = f"#{category_name}\n\n"
        message += f"📝 <b>Name:</b> {name}\n\n"
        message += f"📋 <b>Description:</b>\n{description}\n\n"
        message += f"🎮 <b>Version:</b> {mc_version}\n\n"
        message += f"📥 <b>Download Link:</b>\n{download_link}"

    return message


def format_modern_message(name, description, mc_version, download_link, category_name, lang):
    """التنسيق الحديث"""
    if lang == "ar":
        message = f"✨ <b>{category_name}</b> ✨\n"
        message += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
        message += f"🎯 <b>{name}</b>\n\n"
        message += f"💬 <i>{description}</i>\n\n"
        message += f"🔧 <b>متوافق مع:</b> <code>{mc_version}</code>\n\n"
        message += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
        message += f"⬇️ <b>تحميل الآن:</b> {download_link}"
    else:
        message = f"✨ <b>{category_name}</b> ✨\n"
        message += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
        message += f"🎯 <b>{name}</b>\n\n"
        message += f"💬 <i>{description}</i>\n\n"
        message += f"🔧 <b>Compatible with:</b> <code>{mc_version}</code>\n\n"
        message += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
        message += f"⬇️ <b>Download Now:</b> {download_link}"

    return message


def format_elegant_message(name, description, mc_version, download_link, category_name, lang):
    """التنسيق الأنيق"""
    if lang == "ar":
        message = f"#{category_name}   {{   {name}  .}}  💙\n\n"
        message += f"𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳\n"
        message += f"معلومات ✏️ : \n"
        message += f"{{ {description}  }} 🍎\n"
        message += f"𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳\n\n"
        message += f"لـ اصداࢪ  {{  {mc_version}  }} ✔️\n\n"
        message += f"𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳\n\n"
        message += f"يمكنك تحميل المود عبر الملف الاتي 🌟\n\n"
        message += f"{download_link}"
    else:
        message = f"#{category_name}   {{   {name}  .}}  💙\n\n"
        message += f"𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳\n"
        message += f"Information ✏️ : \n"
        message += f"{{ {description}  }} 🍎\n"
        message += f"𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳\n\n"
        message += f"For Version  {{  {mc_version}  }} ✔️\n\n"
        message += f"𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳𓏳\n\n"
        message += f"You can download the mod via the following file 🌟\n\n"
        message += f"{download_link}"

    return message


def format_minimal_message(name, description, mc_version, download_link, category_name, lang):
    """التنسيق البسيط"""
    if lang == "ar":
        message = f"🎯 <b>{name}</b>\n\n"
        message += f"{description}\n\n"
        message += f"📌 {mc_version}\n"
        message += f"🔗 {download_link}"
    else:
        message = f"🎯 <b>{name}</b>\n\n"
        message += f"{description}\n\n"
        message += f"📌 {mc_version}\n"
        message += f"🔗 {download_link}"

    return message


def format_gaming_message(name, description, mc_version, download_link, category_name, lang):
    """تنسيق الألعاب"""
    if lang == "ar":
        message = f"#{category_name} الهمر ⛏️ . \n\n"
        message += f"الـمعلـومات🌐: \n"
        message += f"▬▭▬▭▬▭▬▭▬▭▬▭▬▭▬\n"
        message += f"{description} 💃\n\n"
        message += f"▬▭▬▭▬▭▬▭▬▭▬▭▬▭▬\n\n"
        message += f"لـ اصـداࢪ +{mc_version} ❤️\n\n"
        message += f"▬▭▬▭▬▭▬▭▬▭▬▭▬▭▬\n"
        message += f"يمكنك تحميل المود عبر ‌ ﴿•⁠ ⁠▽⁠ ⁠•﴾ : ✔️\n\n"
        message += f"{download_link}"
    else:
        message = f"#{category_name} {name} ⛏️ . \n\n"
        message += f"Information🌐: \n"
        message += f"▬▭▬▭▬▭▬▭▬▭▬▭▬▭▬\n"
        message += f"{description} 💃\n\n"
        message += f"▬▭▬▭▬▭▬▭▬▭▬▭▬▭▬\n\n"
        message += f"For Version +{mc_version} ❤️\n\n"
        message += f"▬▭▬▭▬▭▬▭▬▭▬▭▬▭▬\n"
        message += f"You can download the mod via ‌ ﴿•⁠ ⁠▽⁠ ⁠•﴾ : ✔️\n\n"
        message += f"{download_link}"

    return message


# --- دوال اختيار تنسيق الرسالة ---

async def show_message_format_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة اختيار تنسيق الرسالة"""
    query = update.callback_query
    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # الحصول على التنسيق المختار حالياً
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_format = user_data.get('message_format', 'classic')

    # النصوص
    texts = {
        "ar": {
            "title": "🎨 <b>اختر تنسيق الرسالة:</b>",
            "description": "اختر التنسيق الذي تريد استخدامه لنشر المودات في قناتك:",
            "preview": "👁 معاينة التنسيق",
            "continue": "➡️ متابعة",
            "back": "🔙 العودة"
        },
        "en": {
            "title": "🎨 <b>Choose Message Format:</b>",
            "description": "Select the format you want to use for publishing mods in your channel:",
            "preview": "👁 Preview Format",
            "continue": "➡️ Continue",
            "back": "🔙 Back"
        }
    }
    text = texts.get(lang, texts['ar'])

    # قائمة التنسيقات المتاحة
    format_options = {
        "classic": {
            "ar": "🔷 كلاسيكي",
            "en": "🔷 Classic"
        },
        "modern": {
            "ar": "✨ حديث",
            "en": "✨ Modern"
        },
        "elegant": {
            "ar": "💎 أنيق",
            "en": "💎 Elegant"
        },
        "minimal": {
            "ar": "🎯 بسيط",
            "en": "🎯 Minimal"
        },
        "gaming": {
            "ar": "🎮 ألعاب",
            "en": "🎮 Gaming"
        }
    }

    # إنشاء الأزرار
    keyboard = []

    # أزرار التنسيقات (صفين)
    row1 = []
    row2 = []
    row3 = []

    format_keys = list(format_options.keys())
    for i, format_key in enumerate(format_keys):
        is_selected = format_key == selected_format
        button_text = f"{'✅' if is_selected else '☐'} {format_options[format_key][lang]}"
        callback_data = f"select_format_{format_key}"

        if i < 2:  # أول صفين
            row1.append(InlineKeyboardButton(button_text, callback_data=callback_data))
        elif i < 4:  # ثاني صفين
            row2.append(InlineKeyboardButton(button_text, callback_data=callback_data))
        else:  # باقي الأزرار
            row3.append(InlineKeyboardButton(button_text, callback_data=callback_data))

    if row1:
        keyboard.append(row1)
    if row2:
        keyboard.append(row2)
    if row3:
        keyboard.append(row3)

    # زر المعاينة
    keyboard.append([InlineKeyboardButton(text["preview"], callback_data="preview_format")])

    # أزرار التنقل
    keyboard.append([
        InlineKeyboardButton(text["back"], callback_data="back_to_versions"),
        InlineKeyboardButton(text["continue"], callback_data="finish_format_selection")
    ])

    # تكوين الرسالة
    message = f"{text['title']}\n\n{text['description']}"

    reply_markup = InlineKeyboardMarkup(keyboard)

    # عرض الرسالة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def select_message_format(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تحديد تنسيق الرسالة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # استخراج التنسيق من callback_data
    format_type = query.data.split("_")[-1]  # select_format_classic -> classic

    # تحديث التنسيق
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    user_data['message_format'] = format_type
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await show_message_format_selection(update, context)


async def preview_message_format(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """معاينة تنسيق الرسالة المختار"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # الحصول على التنسيق المختار
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_format = user_data.get('message_format', 'classic')

    # بيانات مود تجريبية للمعاينة
    sample_mod = {
        "name": "Alylica's Dungeons",
        "description": "تهدف Alylica's Dungeons إلى مزج محتوى Minecraft Dungeons مع أسلوب وتدفق Minecraft الفانيليا، وتفتخر بمجموعة كبيرة من الأسلحة والأعداء والزعماء الأقوياء - إذا كنت من محبي Minecraft Dungeons وتلعب في البقاء على قيد الحياة، فقد تكون هذه الوظيفة الإضافية مناسبة لك!",
        "mc_version": "1.21.80 - 1.21.72",
        "download_link": "https://example.com/download",
        "category": "addons"
    }

    # إنشاء الرسالة المنسقة
    formatted_message = format_mod_message(sample_mod, selected_format, lang)

    # النصوص
    preview_texts = {
        "ar": {
            "title": "👁 <b>معاينة التنسيق</b>",
            "note": "📝 <i>هذه معاينة لكيفية ظهور المودات بالتنسيق المختار:</i>",
            "back": "🔙 العودة للاختيار"
        },
        "en": {
            "title": "👁 <b>Format Preview</b>",
            "note": "📝 <i>This is a preview of how mods will appear with the selected format:</i>",
            "back": "🔙 Back to Selection"
        }
    }
    text = preview_texts.get(lang, preview_texts['ar'])

    # تكوين الرسالة النهائية
    final_message = f"{text['title']}\n\n{text['note']}\n\n{'-'*30}\n\n{formatted_message}"

    keyboard = [[InlineKeyboardButton(text["back"], callback_data="back_to_format_selection")]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # عرض المعاينة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, final_message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def back_to_versions_from_format(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """العودة لاختيار الإصدارات من اختيار التنسيق"""
    query = update.callback_query
    await query.answer()

    # العودة لعرض قائمة الإصدارات
    await show_mc_versions_selection(update, context)


async def back_to_format_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """العودة لاختيار التنسيق من المعاينة"""
    query = update.callback_query
    await query.answer()

    # العودة لعرض قائمة التنسيقات
    await show_message_format_selection(update, context)


async def finish_format_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """إنهاء اختيار التنسيق والانتقال لاختيار لغة القناة"""
    query = update.callback_query
    await query.answer()

    # الانتقال لاختيار لغة القناة
    await show_channel_language_selection(update, context)


# --- دوال اختيار لغة القناة ---

async def show_channel_language_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة اختيار لغة القناة"""
    query = update.callback_query
    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # الحصول على لغة القناة المختارة حالياً
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_channel_lang = user_data.get('channel_lang', None)

    # إذا لم تكن محددة، استخدم لغة المستخدم الرئيسية كافتراضي
    if selected_channel_lang is None:
        selected_channel_lang = lang

    # النصوص
    texts = {
        "ar": {
            "title": "🌐 <b>اختر لغة القناة:</b>",
            "description": "اختر اللغة التي تريد استخدامها لنشر المودات في قناتك:\n\n📌 هذا سيحدد لغة الوصف والنصوص في المنشورات.",
            "arabic": "🇸🇦 العربية",
            "english": "🇺🇸 الإنجليزية",
            "continue": "➡️ متابعة",
            "back": "🔙 العودة"
        },
        "en": {
            "title": "🌐 <b>Choose Channel Language:</b>",
            "description": "Select the language you want to use for publishing mods in your channel:\n\n📌 This will determine the language of descriptions and texts in posts.",
            "arabic": "🇸🇦 Arabic",
            "english": "🇺🇸 English",
            "continue": "➡️ Continue",
            "back": "🔙 Back"
        }
    }
    text = texts.get(lang, texts['ar'])

    # إنشاء الأزرار
    keyboard = []

    # أزرار اللغات
    arabic_selected = selected_channel_lang == "ar"
    english_selected = selected_channel_lang == "en"

    keyboard.append([
        InlineKeyboardButton(
            f"{'✅' if arabic_selected else '☐'} {text['arabic']}",
            callback_data="select_channel_lang_ar"
        ),
        InlineKeyboardButton(
            f"{'✅' if english_selected else '☐'} {text['english']}",
            callback_data="select_channel_lang_en"
        )
    ])

    # أزرار التنقل
    keyboard.append([
        InlineKeyboardButton(text["back"], callback_data="back_to_format"),
        InlineKeyboardButton(text["continue"], callback_data="finish_channel_lang_selection")
    ])

    # تكوين الرسالة
    message = f"{text['title']}\n\n{text['description']}"

    reply_markup = InlineKeyboardMarkup(keyboard)

    # عرض الرسالة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def select_channel_language(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تحديد لغة القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # استخراج اللغة من callback_data
    channel_lang = query.data.split("_")[-1]  # select_channel_lang_ar -> ar

    # تحديث لغة القناة
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    user_data['channel_lang'] = channel_lang
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await show_channel_language_selection(update, context)


async def back_to_format_from_lang(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """العودة لاختيار التنسيق من اختيار لغة القناة"""
    query = update.callback_query
    await query.answer()

    # العودة لعرض قائمة التنسيقات
    await show_message_format_selection(update, context)


async def finish_channel_lang_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """إنهاء اختيار لغة القناة وعرض رسالة الترحيب النهائية"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # الحصول على البيانات من context
    channel_id = context.user_data.get("pending_channel_id")
    channel_title = context.user_data.get("pending_channel_title", "القناة المحددة")
    interval_minutes = context.user_data.get("pending_interval", 60)

    # تنسيق عرض الفاصل الزمني
    if interval_minutes == 1:
        interval_display_text = "كل دقيقة" if lang == "ar" else "Every minute"
    elif interval_minutes == 30:
        interval_display_text = "كل نصف ساعة" if lang == "ar" else "Every 30 minutes"
    elif interval_minutes == 60:
        interval_display_text = "كل ساعة واحدة" if lang == "ar" else "Every 1 hour"
    elif interval_minutes == 90:
        interval_display_text = "كل ساعة ونصف" if lang == "ar" else "Every 90 minutes"
    elif interval_minutes == 120:
        interval_display_text = "كل ساعتين" if lang == "ar" else "Every 2 hours"
    elif interval_minutes == 180:
        interval_display_text = "كل 3 ساعات" if lang == "ar" else "Every 3 hours"
    elif interval_minutes == 240:
        interval_display_text = "كل 4 ساعات" if lang == "ar" else "Every 4 hours"
    elif interval_minutes == 300:
        interval_display_text = "كل 5 ساعات" if lang == "ar" else "Every 5 hours"
    elif interval_minutes == 360:
        interval_display_text = "كل 6 ساعات" if lang == "ar" else "Every 6 hours"
    elif interval_minutes == 480:
        interval_display_text = "كل 8 ساعات" if lang == "ar" else "Every 8 hours"
    elif interval_minutes == 540:
        interval_display_text = "كل 9 ساعات" if lang == "ar" else "Every 9 hours"
    elif interval_minutes == 600:
        interval_display_text = "كل 10 ساعات" if lang == "ar" else "Every 10 hours"
    elif interval_minutes == 720:
        interval_display_text = "كل 12 ساعة" if lang == "ar" else "Every 12 hours"
    elif interval_minutes == 1080:
        interval_display_text = "كل 18 ساعة" if lang == "ar" else "Every 18 hours"
    elif interval_minutes == 1440:
        interval_display_text = "كل 24 ساعة" if lang == "ar" else "Every 24 hours"
    elif interval_minutes % 60 == 0:
        hours = interval_minutes // 60
        interval_display_text = f"كل {hours} ساعات" if lang == "ar" else f"Every {hours} hours"
    else:
        interval_display_text = f"كل {interval_minutes} دقيقة" if lang == "ar" else f"Every {interval_minutes} minutes"

    # الحصول على التصنيفات والإصدارات والتنسيق ولغة القناة المختارة
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_categories = user_data.get('mod_categories', ['addons', 'shaders', 'texture_packs', 'seeds', 'maps'])
    selected_versions = user_data.get('mc_versions', ['1.21+', '1.20+', '1.19+', '1.18+'])
    selected_format = user_data.get('message_format', 'classic')
    selected_channel_lang = user_data.get('channel_lang', lang)

    # تنسيق عرض التصنيفات
    category_names = {
        "ar": {
            "addons": "إضافات",
            "shaders": "شيدرات",
            "texture_packs": "حزم النسيج",
            "seeds": "البذور",
            "maps": "الخرائط"
        },
        "en": {
            "addons": "Add-ons",
            "shaders": "Shaders",
            "texture_packs": "Texture Packs",
            "seeds": "Seeds",
            "maps": "Maps"
        }
    }

    # تنسيق عرض التنسيقات
    format_names = {
        "ar": {
            "classic": "كلاسيكي",
            "modern": "حديث",
            "elegant": "أنيق",
            "minimal": "بسيط",
            "gaming": "ألعاب"
        },
        "en": {
            "classic": "Classic",
            "modern": "Modern",
            "elegant": "Elegant",
            "minimal": "Minimal",
            "gaming": "Gaming"
        }
    }

    # تنسيق عرض اللغات
    lang_names = {
        "ar": {
            "ar": "العربية",
            "en": "الإنجليزية"
        },
        "en": {
            "ar": "Arabic",
            "en": "English"
        }
    }

    categories_display = ", ".join([category_names[lang][cat] for cat in selected_categories if cat in category_names[lang]])
    versions_display = ", ".join(selected_versions)
    format_display = format_names[lang].get(selected_format, selected_format)
    channel_lang_display = lang_names[lang].get(selected_channel_lang, selected_channel_lang)

    # رسالة الترحيب النهائية
    welcome_texts = {
        "ar": {
            "title": "🎉 تم إعداد البوت بنجاح!",
            "body": "تم ربط قناتك ({}) بنجاح وضبط الإعدادات التالية:\n\n"
                    "⏱️ <b>الفاصل الزمني:</b> {}\n"
                    "🎯 <b>نوع المحتوى:</b> {}\n"
                    "🎮 <b>إصدارات Minecraft:</b> {}\n"
                    "🎨 <b>تنسيق الرسالة:</b> {}\n"
                    "🌐 <b>لغة القناة:</b> {}\n\n"
                    "📝 <b>كيف يعمل البوت؟</b>\n"
                    "- سيقوم البوت الآن تلقائياً بنشر المحتوى المحدد في قناتك حسب الفاصل الزمني المحدد.\n"
                    "- يمكنك دائماً التحكم في جميع الإعدادات من القائمة الرئيسية.\n\n"
                    "اضغط على الزر أدناه للوصول إلى القائمة الرئيسية أو استخدم الأمر /menu.",
            "menu_button": "🏠 القائمة الرئيسية"
        },
        "en": {
            "title": "🎉 Bot Setup Complete!",
            "body": "Your channel ({}) has been successfully linked with the following settings:\n\n"
                    "⏱️ <b>Interval:</b> {}\n"
                    "🎯 <b>Content Type:</b> {}\n"
                    "🎮 <b>Minecraft Versions:</b> {}\n"
                    "🎨 <b>Message Format:</b> {}\n"
                    "🌐 <b>Channel Language:</b> {}\n\n"
                    "📝 <b>How does it work?</b>\n"
                    "- The bot will now automatically publish the selected content to your channel based on the set interval.\n"
                    "- You can always manage all settings from the main menu.\n\n"
                    "Press the button below to access the main menu or use the /menu command.",
            "menu_button": "🏠 Main Menu"
        }
    }

    welcome_text_content = welcome_texts.get(lang, welcome_texts['ar'])

    final_welcome_message = f"<b>{welcome_text_content['title']}</b>\n\n{welcome_text_content['body'].format(html.escape(channel_title or channel_id), interval_display_text, categories_display, versions_display, format_display, channel_lang_display)}"

    keyboard = [[InlineKeyboardButton(welcome_text_content["menu_button"], callback_data="main_menu")]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, final_welcome_message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")

    # تنظيف البيانات المؤقتة
    context.user_data.pop("pending_channel_id", None)
    context.user_data.pop("pending_channel_title", None)
    context.user_data.pop("pending_interval", None)
    context.user_data.pop("waiting_for_channel", None)

    # إرسال إشعار للمسؤول
    logger.info(f"User {user_id} completed setup: Channel {channel_id}, Interval {interval_minutes} minutes, Categories {selected_categories}, Versions {selected_versions}, Format {selected_format}, Channel Language {selected_channel_lang}, User Language {lang}")
    await send_notification(
        f"✅ مستخدم جديد/تحديث قناة:\n"
        f"   - 👤 المستخدم: <code>{user_id}</code>\n"
        f"   - 📢 القناة: {html.escape(channel_title or channel_id)} (<code>{channel_id}</code>)\n"
        f"   - ⏱ الفاصل: {interval_display_text}\n"
        f"   - 🎯 التصنيفات: {categories_display}\n"
        f"   - 🎮 الإصدارات: {versions_display}\n"
        f"   - 🎨 التنسيق: {format_display}\n"
        f"   - 🌐 لغة القناة: {channel_lang_display}\n"
        f"   - 👤 لغة المستخدم: {lang}",
        context
    )


async def set_publish_interval(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Sets the publishing interval via command (e.g., /set_interval 120)."""
    user_id = update.effective_user.id
    lang = get_user_lang(user_id)

    texts = {
        "ar": {
            "usage": "ℹ️ الاستخدام: <code>/set_interval [دقائق]</code>\nمثال: <code>/set_interval 120</code> (للنشر كل ساعتين).",
            "invalid_interval": "⚠️ الفاصل الزمني يجب أن يكون رقمًا صحيحًا موجبًا (عدد الدقائق).",
            "success": "✅ تم تحديث الفاصل الزمني للنشر إلى: كل {} دقيقة.",
            "not_linked": "❌ يرجى ربط قناتك أولاً عبر القائمة الرئيسية.",
            "error": "❌ حدث خطأ أثناء تحديث الإعدادات."
        },
        "en": {
            "usage": "ℹ️ Usage: <code>/set_interval [minutes]</code>\nExample: <code>/set_interval 120</code> (for every 2 hours).",
            "invalid_interval": "⚠️ Interval must be a positive integer number of minutes.",
            "success": "✅ Publishing interval updated to: every {} minutes.",
            "not_linked": "❌ Please link your channel first via the main menu.",
            "error": "❌ An error occurred while updating settings."
        }
    }
    text = texts.get(lang, texts['ar'])

    if not context.args or len(context.args) != 1:
        await update.message.reply_text(text["usage"], parse_mode="HTML")
        return

    try:
        interval_minutes = int(context.args[0])
        if interval_minutes <= 0:
            await update.message.reply_text(text["invalid_interval"], parse_mode="HTML")
            return

        # Use the unified update function
        success = update_publish_settings(user_id, publish_interval_minutes=interval_minutes)

        if success:
            await update.message.reply_text(text["success"].format(interval_minutes), parse_mode="HTML")
            logger.info(f"User {user_id} set interval to {interval_minutes} minutes via command.")
        else:
            # Check if the user exists but has no channel, or doesn't exist at all
            user_channels = load_user_channels()
            if str(user_id) in user_channels:
                 await update.message.reply_text(text["not_linked"], parse_mode="HTML")
            else:
                 # Should not happen if start command initializes user, but maybe if data file corrupts
                 await update.message.reply_text(text["error"], parse_mode="HTML")

    except ValueError:
        await update.message.reply_text(text["invalid_interval"], parse_mode="HTML")
    except Exception as e:
         logger.error(f"Error in set_publish_interval command for user {user_id}: {e}", exc_info=True)
         await update.message.reply_text(text["error"], parse_mode="HTML")


async def status(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Displays the bot's status for the user."""
    if not update.message: return # Only respond to commands
    user_id = update.effective_user.id
    lang = get_user_lang(user_id)
    user_channels_data = load_user_channels()

    texts = {
        "ar": {
            "title": "📊 <b>حالة البوت الحالية:</b>",
            "linked_channel": "القناة المرتبطة:",
            "interval": "الفاصل الزمني للنشر:",
            "status": "حالة النشر:",
            "preview": "معاينة قبل النشر:",
            "last_publish": "آخر نشر في قناتك:",
            "not_set": "غير محدد",
            "not_yet": "لم يتم النشر بعد",
            "active": "مفعل ✅",
            "inactive": "متوقف ❌",
            "enabled": "مفعلة ✅",
            "disabled": "متوقفة ❌",
            "unavailable": "⚠️ القناة غير متاحة أو تم تغيير معرفها!",
            "not_linked": "لم تقم بربط قناة بعد. استخدم القائمة الرئيسية.",
            "total_users": "إجمالي المستخدمين المسجلين:",
            "pending_queue": "مودات تنتظر النشر في قناتك:"
        },
        "en": {
            "title": "📊 <b>Current Bot Status:</b>",
            "linked_channel": "Linked Channel:",
            "interval": "Publishing Interval:",
            "status": "Publishing Status:",
            "preview": "Preview Before Publish:",
            "last_publish": "Last Published to Your Channel:",
            "not_set": "Not set",
            "not_yet": "Not published yet",
            "active": "Active ✅",
            "inactive": "Inactive ❌",
            "enabled": "Enabled ✅",
            "disabled": "Disabled ❌",
            "unavailable": "⚠️ Channel unavailable or ID changed!",
            "not_linked": "You haven't linked a channel yet. Use the main menu.",
            "total_users": "Total registered users:",
            "pending_queue": "Mods waiting to publish to your channel:"
        }
    }
    text = texts.get(lang, texts['ar'])

    status_text = f"{text['title']}\n\n"
    user_data = user_channels_data.get(str(user_id))

    if isinstance(user_data, dict) and user_data.get("channel_id"):
        channel_id = user_data["channel_id"]
        interval_minutes = user_data.get('publish_interval', 60) # Default 60 mins
        is_active = user_data.get('active', True)
        preview_enabled = user_data.get('preview_enabled', False)
        last_publish_time_str = user_data.get("last_publish_time")

        # Format interval display
        if interval_minutes == 1:
            interval_str = "كل دقيقة" if lang == "ar" else "Every minute"
        elif interval_minutes == 30:
            interval_str = "كل نصف ساعة" if lang == "ar" else "Every 30 minutes"
        elif interval_minutes == 60:
            interval_str = "كل ساعة واحدة" if lang == "ar" else "Every 1 hour"
        elif interval_minutes == 90:
            interval_str = "كل ساعة ونصف" if lang == "ar" else "Every 90 minutes"
        elif interval_minutes == 120:
            interval_str = "كل ساعتين" if lang == "ar" else "Every 2 hours"
        elif interval_minutes == 180:
            interval_str = "كل 3 ساعات" if lang == "ar" else "Every 3 hours"
        elif interval_minutes == 240:
            interval_str = "كل 4 ساعات" if lang == "ar" else "Every 4 hours"
        elif interval_minutes == 300:
            interval_str = "كل 5 ساعات" if lang == "ar" else "Every 5 hours"
        elif interval_minutes == 360:
            interval_str = "كل 6 ساعات" if lang == "ar" else "Every 6 hours"
        elif interval_minutes == 480:
            interval_str = "كل 8 ساعات" if lang == "ar" else "Every 8 hours"
        elif interval_minutes == 540:
            interval_str = "كل 9 ساعات" if lang == "ar" else "Every 9 hours"
        elif interval_minutes == 600:
            interval_str = "كل 10 ساعات" if lang == "ar" else "Every 10 hours"
        elif interval_minutes == 720:
            interval_str = "كل 12 ساعة" if lang == "ar" else "Every 12 hours"
        elif interval_minutes == 1080:
            interval_str = "كل 18 ساعة" if lang == "ar" else "Every 18 hours"
        elif interval_minutes == 1440:
            interval_str = "كل 24 ساعة" if lang == "ar" else "Every 24 hours"
        elif interval_minutes % 60 == 0:
            hours = interval_minutes // 60
            interval_str = f"كل {hours} " + ("ساعة" if hours == 1 else "ساعات") if lang == "ar" else f"Every {hours} hour{'s' if hours > 1 else ''}"
        else:
           interval_str = f"كل {interval_minutes} دقيقة" if lang == "ar" else f"Every {interval_minutes} minutes"

        # Format last publish time
        last_publish_display = text["not_yet"]
        if last_publish_time_str:
            try:
                last_publish_dt = datetime.fromisoformat(last_publish_time_str)
                # Format to a readable string, maybe relative time? For now, simple format.
                last_publish_display = last_publish_dt.strftime("%Y-%m-%d %H:%M:%S %Z")
            except ValueError:
                last_publish_display = text["not_set"] # Or indicate invalid format

        # Try to get channel title
        channel_display = f"<code>{channel_id}</code>"
        try:
            # Use timeout
            chat = await asyncio.wait_for(context.bot.get_chat(channel_id), timeout=5.0)
            channel_display = f"{chat.title or 'اسم غير معروف'} (<code>{channel_id}</code>)"
        except asyncio.TimeoutError:
            channel_display += f" ({text['unavailable']})"
            logger.warning(f"Timeout getting chat info for {channel_id} in status command")
        except Exception:
            channel_display += f" ({text['unavailable']})"
            logger.warning(f"Could not get chat info for {channel_id} in status command")

        status_text += f"🔸 {text['linked_channel']} {channel_display}\n"
        status_text += f"⏱ {text['interval']} {interval_str}\n"
        status_text += f"⏯️ {text['status']} {text['active'] if is_active else text['inactive']}\n"
        status_text += f"👁️ {text['preview']} {text['enabled'] if preview_enabled else text['disabled']}\n"
        status_text += f"🕒 {text['last_publish']} {last_publish_display}\n"

        # Show count of pending mods for this user
        pending_user = load_pending_publications()
        user_pending_count = sum(1 for item in pending_user if item['user_id'] == str(user_id))
        status_text += f"⏳ {text['pending_queue']} {user_pending_count}\n"

    else:
        status_text += f"{text['not_linked']}\n"

    # General stats (optional)
    # status_text += f"\n🌍 {text['total_users']} {len(user_channels_data)}"

    await update.message.reply_text(status_text, parse_mode="HTML")

# --- Main User Interface ---

async def show_main_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Displays the main menu with language support."""
    user_id = update.effective_user.id
    lang = get_user_lang(user_id)

    menu_texts = {
        "ar": {
            "welcome": "🏠 <b>القائمة الرئيسية</b>\n\nاختر الإجراء المطلوب:",
            "buttons": {
                "manage_channel": "⚙️ إدارة القناة والإعدادات", # Combined
                "language": "🌐 تغيير اللغة",
                "help": "❓ مساعدة ودعم"
            }
        },
        "en": {
            "welcome": "🏠 <b>Main Menu</b>\n\nChoose an action:",
            "buttons": {
                "manage_channel": "⚙️ Manage Channel & Settings", # Combined
                "language": "🌐 Change Language",
                "help": "❓ Help & Support"
            }
        }
    }

    text = menu_texts.get(lang, menu_texts['ar'])
    buttons = text['buttons']

    keyboard = [
        [InlineKeyboardButton(buttons["manage_channel"], callback_data="manage_channel_menu")],
        [InlineKeyboardButton(buttons["language"], callback_data="change_language_menu")],
        [InlineKeyboardButton(buttons["help"], callback_data="help_menu")]
    ]

    # Add admin button if the user is the admin
    if str(user_id) == YOUR_CHAT_ID:
         keyboard.insert(0, [InlineKeyboardButton("👑 لوحة تحكم المسؤول", callback_data="admin_panel")])


    reply_markup = InlineKeyboardMarkup(keyboard)

    # Use update_ui_response for consistency if called from callback
    if update.callback_query:
        is_photo = bool(update.callback_query.message and update.callback_query.message.photo)
        await update_ui_response(update.callback_query, text["welcome"], is_photo=is_photo, reply_markup=reply_markup)
    elif update.message:
        await update.message.reply_text(text=text["welcome"], reply_markup=reply_markup, parse_mode="HTML")


async def change_language_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Shows the language selection menu."""
    query = update.callback_query
    await query.answer()
    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    texts = {
        "ar": {"title": "اختر اللغة الجديدة:", "back": "🔙 العودة للقائمة الرئيسية"},
        "en": {"title": "Select new language:", "back": "🔙 Back to Main Menu"}
    }
    text = texts.get(lang, texts['ar'])

    keyboard = [
        [
            InlineKeyboardButton("🇸🇦 العربية", callback_data="lang_ar"),
            InlineKeyboardButton("🇬🇧 English", callback_data="lang_en")
        ],
        [InlineKeyboardButton(text["back"], callback_data="main_menu")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, text["title"], is_photo=is_photo, reply_markup=reply_markup)


import asyncio
import logging
import html # Import html module for escaping
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import BadRequest, TelegramError, Forbidden # تأكد من وجود Forbidden هنا

# --- افترض أن هذه الدوال معرفة في مكان آخر في الملف ---
# from your_module import (
#     get_user_lang,
#     load_user_channels,
#     update_ui_response,
#     logger # افترض أن logger معرف أيضاً
# )
# --- نهاية الافتراضات ---

async def show_channel_settings_for_user(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Displays the specific settings menu for the user's linked channel."""
    query = update.callback_query
    # --- إضافة: حاول الرد على الاستعلام مبكرًا لتجنب "request timed out" ---
    try:
        await query.answer()
    except Exception as answer_err:
        logger.warning(f"Could not answer callback query {query.id} initially: {answer_err}")
    # --- نهاية الإضافة ---

    user_id = query.from_user.id
    lang = get_user_lang(user_id) # افتراض أن get_user_lang معرفة
    user_channels = load_user_channels() # افتراض أن load_user_channels معرفة
    user_data = user_channels.get(str(user_id))

    # --- النصوص ---
    texts = {
        "ar": {
            "no_channel": "ليس لديك قناة مرتبطة لعرض إعداداتها. استخدم زر 'إدارة القناة' لإضافة واحدة.",
            "back": "🔙 العودة للقائمة",
            "change_interval": "⏱ تغيير الفاصل الزمني",
            "manage_categories": "🎯 إدارة نوع المحتوى",
            "manage_versions": "🎮 إدارة إصدارات Minecraft",
            "manage_format": "🎨 إدارة تنسيق الرسالة",
            "manage_language": "🌐 إدارة لغة القناة",
            "toggle_publish": "⏯ تفعيل/إيقاف النشر",
            "toggle_preview": "👁 تفعيل/إيقاف المعاينة",
            "delete_channel": "🗑 إلغاء ربط القناة",
            "settings_title": "🔧 <b>إعدادات القناة:</b>",
            "channel_id_label": "القناة:",
            "publish_state_label": "حالة النشر:",
            "preview_state_label": "معاينة المودات:",
            "interval_label": "الفاصل الزمني:",
            "categories_label": "نوع المحتوى:",
            "versions_label": "إصدارات Minecraft:",
            "active": "مفعل ✅",
            "inactive": "متوقف ❌",
            "1m": "كل دقيقة", "30m": "كل نصف ساعة", "hourly": "كل ساعة", "2h": "كل ساعتين", "3h": "كل 3 ساعات", "6h": "كل 6 ساعات", "12h": "كل 12 ساعة",
            "nhours": "كل {} ساعات", "nminutes": "كل {} دقيقة",
            "offline_title": "🔧 <b>إعدادات القناة (غير متصلة):</b>"
        },
        "en": {
            "no_channel": "You don't have a linked channel to show settings for. Use 'Manage Channel' to add one.",
            "back": "🔙 Back to Menu",
            "change_interval": "⏱ Change Interval",
            "manage_categories": "🎯 Manage Content Type",
            "manage_versions": "🎮 Manage Minecraft Versions",
            "manage_format": "🎨 Manage Message Format",
            "manage_language": "🌐 Manage Channel Language",
            "toggle_publish": "⏯ Toggle Publishing",
            "toggle_preview": "👁 Toggle Preview",
            "delete_channel": "🗑 Unlink Channel",
            "settings_title": "🔧 <b>Channel Settings:</b>",
            "channel_id_label": "Channel:",
            "publish_state_label": "Publishing Status:",
            "preview_state_label": "Preview Mods Status:",
            "interval_label": "Interval:",
            "categories_label": "Content Type:",
            "versions_label": "Minecraft Versions:",
            "active": "Active ✅", "inactive": "Inactive ❌",
            "1m": "Every minute", "30m": "Every 30 minutes", "hourly": "Hourly", "2h": "Every 2 hours", "3h": "Every 3 hours", "6h": "Every 6 hours", "12h": "Every 12 hours",
            "nhours": "Every {} hours", "nminutes": "Every {} minutes",
            "offline_title": "🔧 <b>Channel Settings (Offline):</b>"
        }
    }
    text = texts.get(lang, texts['ar'])

    # --- التحقق من وجود بيانات القناة ---
    if not isinstance(user_data, dict) or not user_data.get("channel_id"):
        is_photo = bool(query.message and query.message.photo)
        # استدعاء update_ui_response (يفترض أنها معرفة ومعدلة لتقبل parse_mode)
        await update_ui_response(
            query,
            text["no_channel"],
            is_photo=is_photo,
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(text["back"], callback_data="main_menu")]])
            # لا نحتاج parse_mode هنا لأن النص بسيط
        )
        return

    # --- استخراج البيانات وبناء لوحة المفاتيح ---
    channel_id = user_data.get("channel_id")
    channel_active = user_data.get("active", True)
    preview_enabled = user_data.get("preview_enabled", False)
    publish_interval_minutes = user_data.get("publish_interval", 60)
    selected_categories = user_data.get('mod_categories', ['addons', 'shaders', 'texture_packs', 'seeds', 'maps'])
    selected_versions = user_data.get('mc_versions', ['1.21+', '1.20+', '1.19+', '1.18+'])
    selected_format = user_data.get('message_format', 'classic')
    selected_channel_lang = user_data.get('channel_lang', lang)

    keyboard = [
        [InlineKeyboardButton(text["toggle_preview"] + (" ✅" if preview_enabled else " ❌"), callback_data="setting_toggle_preview")],
        [InlineKeyboardButton(text["change_interval"], callback_data="setting_change_interval_menu")],
        [InlineKeyboardButton(text["manage_categories"], callback_data="setting_manage_categories")],
        [InlineKeyboardButton(text["manage_versions"], callback_data="setting_manage_versions")],
        [InlineKeyboardButton(text["manage_format"], callback_data="setting_manage_format")],
        [InlineKeyboardButton(text["manage_language"], callback_data="setting_manage_language")],
        [InlineKeyboardButton(text["toggle_publish"] + (" ✅" if channel_active else " ❌"), callback_data="setting_toggle_publish")],
        [InlineKeyboardButton(text["delete_channel"], callback_data="setting_delete_channel_confirm")],
        [InlineKeyboardButton(text["back"], callback_data="main_menu")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # --- تنسيق النصوص ---
    status_text = text["active"] if channel_active else text["inactive"]
    # --- تصحيح منطق النص للمعاينة ---
    # يجب أن يعكس النص حالة تفعيل المعاينة (preview_enabled)
    preview_text = text["active"] if preview_enabled else text["inactive"]
    # --- نهاية التصحيح ---

    # تنسيق الفاصل الزمني
    if publish_interval_minutes == 1: interval_text = text.get("1m", "Every minute")
    elif publish_interval_minutes == 30: interval_text = text.get("30m", "Every 30 minutes")
    elif publish_interval_minutes == 60: interval_text = text.get("hourly", "Hourly")
    elif publish_interval_minutes == 90: interval_text = text.get("90m", "Every 90 minutes")
    elif publish_interval_minutes == 120: interval_text = text.get("2h", "Every 2 hours")
    elif publish_interval_minutes == 180: interval_text = text.get("3h", "Every 3 hours")
    elif publish_interval_minutes == 240: interval_text = text.get("4h", "Every 4 hours")
    elif publish_interval_minutes == 300: interval_text = text.get("5h", "Every 5 hours")
    elif publish_interval_minutes == 360: interval_text = text.get("6h", "Every 6 hours")
    elif publish_interval_minutes == 480: interval_text = text.get("8h", "Every 8 hours")
    elif publish_interval_minutes == 540: interval_text = text.get("9h", "Every 9 hours")
    elif publish_interval_minutes == 600: interval_text = text.get("10h", "Every 10 hours")
    elif publish_interval_minutes == 720: interval_text = text.get("12h", "Every 12 hours")
    elif publish_interval_minutes == 1080: interval_text = text.get("18h", "Every 18 hours")
    elif publish_interval_minutes == 1440: interval_text = text.get("24h", "Every 24 hours")
    elif publish_interval_minutes % 60 == 0:
         hours = publish_interval_minutes // 60
         # استخدام get مع قيمة افتراضية لتجنب الأخطاء إذا لم يتم العثور على المفتاح
         interval_text = text.get("hourly", "Hourly") if hours == 1 else text.get("nhours", "Every {} hours").format(hours)
    else:
         interval_text = text.get("nminutes", "Every {} minutes").format(publish_interval_minutes)

    # --- بناء محتوى الرسالة ---
    message_content = ""
    channel_display = f"<code>{channel_id}</code>"
    channel_title = None # متغير لتخزين اسم القناة لاحقاً إن وجد
    try:
        chat = await asyncio.wait_for(context.bot.get_chat(channel_id), timeout=5.0)
        channel_title = chat.title or 'Unknown Name' # احفظ الاسم لاستخدامه
        channel_display = f"{html.escape(channel_title)} (<code>{channel_id}</code>)"
        message_content += f"{text['settings_title']}\n"
    except asyncio.TimeoutError:
         message_content += f"{text['offline_title']}\n(⚠️ تعذر جلب اسم القناة حالياً)\n"
         logger.warning(f"Timeout getting chat info for {channel_id} in show_channel_settings_for_user")
    except Forbidden as e: # التعامل مع خطأ الصلاحيات تحديدًا
        message_content += f"{text['offline_title']}\n(⚠️ البوت ليس لديه الصلاحية للوصول للقناة <code>{channel_id}</code>. قد يكون طُرد أو ليس مشرفاً)\n"
        logger.warning(f"Forbidden error getting chat info for {channel_id} in show_channel_settings_for_user: {e}")
    except (BadRequest, TelegramError) as e:
        message_content += f"{text['offline_title']}\n(⚠️ القناة <code>{channel_id}</code> غير متاحة أو تم تغيير معرفها)\n"
        logger.warning(f"Could not get chat info for {channel_id} in show_channel_settings_for_user: {e}")
    except Exception as e: # الأخطاء الأخرى غير المتوقعة
        message_content += f"{text['offline_title']}\n(⚠️ حدث خطأ غير متوقع عند جلب معلومات القناة)\n"
        logger.error(f"Unexpected error getting chat info for {channel_id} in show_channel_settings_for_user: {e}", exc_info=True)

    # تنسيق عرض التصنيفات
    category_names = {
        "ar": {
            "addons": "إضافات",
            "shaders": "شيدرات",
            "texture_packs": "حزم النسيج",
            "seeds": "البذور",
            "maps": "الخرائط"
        },
        "en": {
            "addons": "Add-ons",
            "shaders": "Shaders",
            "texture_packs": "Texture Packs",
            "seeds": "Seeds",
            "maps": "Maps"
        }
    }

    categories_display = ", ".join([category_names[lang][cat] for cat in selected_categories if cat in category_names[lang]])
    if not categories_display:
        categories_display = "غير محدد" if lang == "ar" else "Not specified"

    # تنسيق عرض الإصدارات
    versions_display = ", ".join(selected_versions)
    if not versions_display:
        versions_display = "غير محدد" if lang == "ar" else "Not specified"

    # تنسيق عرض التنسيقات
    format_names = {
        "ar": {
            "classic": "كلاسيكي",
            "modern": "حديث",
            "elegant": "أنيق",
            "minimal": "بسيط",
            "gaming": "ألعاب"
        },
        "en": {
            "classic": "Classic",
            "modern": "Modern",
            "elegant": "Elegant",
            "minimal": "Minimal",
            "gaming": "Gaming"
        }
    }
    format_display = format_names[lang].get(selected_format, selected_format)

    # تنسيق عرض لغة القناة
    lang_names = {
        "ar": {
            "ar": "العربية",
            "en": "الإنجليزية"
        },
        "en": {
            "ar": "Arabic",
            "en": "English"
        }
    }
    channel_lang_display = lang_names[lang].get(selected_channel_lang, selected_channel_lang)

    message_content += (
        f"\n{text['channel_id_label']} {channel_display}\n"
        f"{text['publish_state_label']} {status_text}\n"
        f"{text['preview_state_label']} {preview_text}\n" # استخدام النص المصحح للمعاينة
        f"{text['interval_label']} {interval_text}\n"
        f"{text['categories_label']} {categories_display}\n"
        f"{text['versions_label']} {versions_display}\n"
        f"{'🎨 تنسيق الرسالة:' if lang == 'ar' else '🎨 Message Format:'} {format_display}\n"
        f"{'🌐 لغة القناة:' if lang == 'ar' else '🌐 Channel Language:'} {channel_lang_display}"
    )

    # --- استدعاء الدالة المساعدة لتحديث الواجهة ---
    # يتم تمرير parse_mode="HTML" لأن message_content يستخدم وسوم HTML مثل <b> و <code>
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(
        query=query,
        text=message_content,
        is_photo=is_photo,
        reply_markup=reply_markup,
        parse_mode="HTML" # <--- هذا هو الوسيط المطلوب
    )


async def toggle_publish_for_user(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toggles publishing for the user's channel (called from settings)."""
    query = update.callback_query
    user_id = query.from_user.id
    user_id_str = str(user_id)
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str)

    if isinstance(user_data, dict) and user_data.get("channel_id"):
        current_status = user_data.get("active", True)
        new_status = not current_status
        # Use the update function
        success = update_publish_settings(user_id, is_active=new_status)
        if success:
            await query.answer(f"تم {'تفعيل' if new_status else 'إيقاف'} النشر التلقائي.", show_alert=False)
            logger.info(f"Toggled publishing for user {user_id} to {new_status}")
            # Re-display the settings menu with updated status
            await show_channel_settings_for_user(update, context)
        else:
             await query.answer("❌ حدث خطأ أثناء تحديث الحالة.", show_alert=True)
             await show_main_menu(update, context) # Go back to main menu on error
    else:
        logger.error(f"Could not find channel data for user {user_id} in toggle_publish_for_user")
        await query.answer("❌ خطأ: لم يتم العثور على بيانات القناة.", show_alert=True)
        await show_main_menu(update, context)


async def toggle_preview_for_user(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toggles preview for the user's channel (called from settings)."""
    query = update.callback_query
    user_id = query.from_user.id
    user_id_str = str(user_id)
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str)

    if isinstance(user_data, dict) and user_data.get("channel_id"):
        current_status = user_data.get("preview_enabled", False)
        new_status = not current_status
        success = update_publish_settings(user_id, preview_enabled=new_status)

        if success:
            await query.answer(f"تم {'تفعيل' if new_status else 'إيقاف'} معاينة المودات.", show_alert=False)
            logger.info(f"Toggled preview for user {user_id} to {new_status}")
            # Re-display the settings menu with updated status
            await show_channel_settings_for_user(update, context)
        else:
            await query.answer("❌ حدث خطأ أثناء تحديث الحالة.", show_alert=True)
            await show_main_menu(update, context) # Go back to main menu on error

    else:
        logger.error(f"Could not find channel data for user {user_id} in toggle_preview_for_user")
        await query.answer("❌ خطأ: لم يتم العثور على بيانات القناة.", show_alert=True)
        await show_main_menu(update, context)


async def change_interval_menu_for_user(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Shows the interval selection menu (called from user channel settings)."""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    texts = {
        "ar": {
            # --- تعديل الأزرار ---
            "1m": "كل دقيقة", "30m": "كل نصف ساعة", "1h": "كل ساعة", "90m": "كل ساعة ونصف",
            "2h": "كل ساعتين", "3h": "كل 3 ساعات", "4h": "كل 4 ساعات",
            "5h": "كل 5 ساعات", "6h": "كل 6 ساعات", "8h": "كل 8 ساعات",
            "9h": "كل 9 ساعات", "10h": "كل 10 ساعات", "12h": "كل 12 ساعة",
            "18h": "كل 18 ساعة", "24h": "كل 24 ساعة",
            # -------------------
            "back": "🔙 العودة للإعدادات",
            "title": "اختر الفاصل الزمني الجديد للنشر:"
        },
        "en": {
             # --- تعديل الأزرار (إنجليزية) ---
            "1m": "Every minute", "30m": "Every 30 min", "1h": "Hourly", "90m": "Every 90 min",
            "2h": "Every 2 hours", "3h": "Every 3 hours", "4h": "Every 4 hours",
            "5h": "Every 5 hours", "6h": "Every 6 hours", "8h": "Every 8 hours",
            "9h": "Every 9 hours", "10h": "Every 10 hours", "12h": "Every 12 hours",
            "18h": "Every 18 hours", "24h": "Every 24 hours",
             # --------------------------
            "back": "🔙 Back to Settings",
            "title": "Select the new publishing interval:"
        }
    }
    text = texts.get(lang, texts['ar'])

    # --- تعديل بناء الأزرار ---
    keyboard = [
        [
            InlineKeyboardButton(text["1m"], callback_data="setting_set_interval_1"),
            InlineKeyboardButton(text["30m"], callback_data="setting_set_interval_30"),
            InlineKeyboardButton(text["1h"], callback_data="setting_set_interval_60"),
        ],
        [
            InlineKeyboardButton(text["90m"], callback_data="setting_set_interval_90"),
            InlineKeyboardButton(text["2h"], callback_data="setting_set_interval_120"),
            InlineKeyboardButton(text["3h"], callback_data="setting_set_interval_180"),
        ],
        [
            InlineKeyboardButton(text["4h"], callback_data="setting_set_interval_240"),
            InlineKeyboardButton(text["5h"], callback_data="setting_set_interval_300"),
            InlineKeyboardButton(text["6h"], callback_data="setting_set_interval_360"),
        ],
        [
            InlineKeyboardButton(text["8h"], callback_data="setting_set_interval_480"),
            InlineKeyboardButton(text["9h"], callback_data="setting_set_interval_540"),
            InlineKeyboardButton(text["10h"], callback_data="setting_set_interval_600"),
        ],
        [
            InlineKeyboardButton(text["12h"], callback_data="setting_set_interval_720"),
            InlineKeyboardButton(text["18h"], callback_data="setting_set_interval_1080"),
            InlineKeyboardButton(text["24h"], callback_data="setting_set_interval_1440"),
        ],
        [InlineKeyboardButton(text["back"], callback_data="show_channel_settings")] # Back to specific channel settings
    ]
    # ------------------------
    reply_markup = InlineKeyboardMarkup(keyboard)
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, text["title"], is_photo=is_photo, reply_markup=reply_markup)


async def set_interval_for_user(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Sets the interval from the user channel settings menu."""
    query = update.callback_query
    await query.answer()

    try:
        interval_minutes = int(query.data.split("_")[-1]) # e.g., setting_set_interval_60
        user_id = query.from_user.id
        lang = get_user_lang(user_id)

        # Update settings for the user using the unified function
        success = update_publish_settings(user_id, publish_interval_minutes=interval_minutes)

        if success:
            logger.info(f"Interval updated to {interval_minutes} minutes for user {user_id} via menu.")
            # Go back to the channel settings menu to show the updated interval
            await show_channel_settings_for_user(update, context)
        else:
            texts = { "ar": {"error": "❌ حدث خطأ أثناء تحديث الفاصل الزمني."},
                      "en": {"error": "❌ Error updating interval."} }
            await query.answer(texts.get(lang, texts['ar'])["error"], show_alert=True)
            # Go back to main menu on error
            await show_main_menu(update, context)

    except (ValueError, IndexError) as e:
         logger.error(f"Error parsing interval from callback data '{query.data}': {e}")
         await query.answer("Error processing selection.", show_alert=True)
    except Exception as e:
         logger.error(f"Unexpected error in set_interval_for_user: {e}", exc_info=True)
         await query.answer("An unexpected error occurred.", show_alert=True)


async def back_to_channel_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles the 'Back' button from the interval selection menu."""
    query = update.callback_query
    await query.answer()
    # Call the specific channel settings display function
    await show_channel_settings_for_user(update, context)


async def manage_categories_from_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة إدارة التصنيفات من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # النصوص
    category_texts = {
        "ar": {
            "title": "🎯 <b>إدارة نوع المحتوى</b>",
            "description": "اختر نوع المحتوى الذي تريد نشره في قناتك:\n\n📌 يمكنك اختيار تصنيف واحد أو عدة تصنيفات أو جميعها.",
            "categories": {
                "addons": "🧩 إضافات (Add-ons)",
                "shaders": "✨ شيدرات (Shaders)",
                "texture_packs": "🎨 حزم النسيج (Texture Packs)",
                "seeds": "🌱 البذور (Seeds)",
                "maps": "🗺️ الخرائط (Maps)"
            },
            "select_all": "✅ تحديد الكل",
            "save": "💾 حفظ التغييرات",
            "back": "🔙 العودة لإعدادات القناة"
        },
        "en": {
            "title": "🎯 <b>Manage Content Type</b>",
            "description": "Choose the type of content you want to publish to your channel:\n\n📌 You can select one category, multiple categories, or all of them.",
            "categories": {
                "addons": "🧩 Add-ons",
                "shaders": "✨ Shaders",
                "texture_packs": "🎨 Texture Packs",
                "seeds": "🌱 Seeds",
                "maps": "🗺️ Maps"
            },
            "select_all": "✅ Select All",
            "save": "💾 Save Changes",
            "back": "🔙 Back to Channel Settings"
        }
    }

    text = category_texts.get(lang, category_texts['ar'])

    # الحصول على التصنيفات المختارة حالياً
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_categories = user_data.get('mod_categories', ['addons', 'shaders', 'texture_packs', 'seeds', 'maps'])

    # بناء الرسالة
    message = f"{text['title']}\n\n{text['description']}\n\n"

    # بناء الأزرار
    keyboard = []

    # أزرار التصنيفات
    for category_key, category_name in text['categories'].items():
        is_selected = category_key in selected_categories
        button_text = f"{'✅' if is_selected else '☐'} {category_name}"
        keyboard.append([InlineKeyboardButton(button_text, callback_data=f"settings_toggle_category_{category_key}")])

    # أزرار التحكم
    keyboard.append([InlineKeyboardButton(text["select_all"], callback_data="settings_select_all_categories")])
    keyboard.append([InlineKeyboardButton(text["save"], callback_data="settings_save_categories")])
    keyboard.append([InlineKeyboardButton(text["back"], callback_data="show_channel_settings")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    # عرض الرسالة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def settings_toggle_category(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تبديل تحديد تصنيف معين من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # استخراج التصنيف من callback_data
    category = query.data.split("_")[-1]  # settings_toggle_category_addons -> addons

    # تحديث التصنيفات
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    selected_categories = user_data.get('mod_categories', ['addons', 'shaders', 'texture_packs', 'seeds', 'maps'])

    if category in selected_categories:
        selected_categories.remove(category)
    else:
        selected_categories.append(category)

    # حفظ التحديث
    user_data['mod_categories'] = selected_categories
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await manage_categories_from_settings(update, context)


async def settings_select_all_categories(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تحديد جميع التصنيفات من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # تحديد جميع التصنيفات
    all_categories = ['addons', 'shaders', 'texture_packs', 'seeds', 'maps']

    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    user_data['mod_categories'] = all_categories
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await manage_categories_from_settings(update, context)


async def settings_save_categories(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """حفظ التصنيفات والعودة لإعدادات القناة"""
    query = update.callback_query
    await query.answer()

    lang = get_user_lang(query.from_user.id)

    success_texts = {
        "ar": "✅ تم حفظ إعدادات نوع المحتوى بنجاح",
        "en": "✅ Content type settings saved successfully"
    }

    await query.answer(success_texts.get(lang, success_texts['ar']), show_alert=True)

    # العودة لإعدادات القناة
    await show_channel_settings_for_user(update, context)


# --- دوال إدارة إصدارات Minecraft من الإعدادات ---

async def manage_versions_from_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة إدارة إصدارات Minecraft من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # النصوص
    version_texts = {
        "ar": {
            "title": "🎮 <b>إدارة إصدارات Minecraft</b>",
            "description": "اختر إصدارات Minecraft التي تريد نشر مودات لها في قناتك:\n\n📌 يمكنك اختيار إصدار واحد أو عدة إصدارات أو جميعها.",
            "select_all": "✅ تحديد الكل",
            "save": "💾 حفظ التغييرات",
            "back": "🔙 العودة لإعدادات القناة"
        },
        "en": {
            "title": "🎮 <b>Manage Minecraft Versions</b>",
            "description": "Choose the Minecraft versions you want to publish mods for in your channel:\n\n📌 You can select one version, multiple versions, or all of them.",
            "select_all": "✅ Select All",
            "save": "💾 Save Changes",
            "back": "🔙 Back to Channel Settings"
        }
    }

    text = version_texts.get(lang, version_texts['ar'])

    # الحصول على الإصدارات المختارة حالياً
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_versions = user_data.get('mc_versions', ['1.21+', '1.20+', '1.19+', '1.18+'])

    # قائمة الإصدارات المتاحة
    available_versions = ['1.21+', '1.20+', '1.19+', '1.18+']

    # بناء الرسالة
    message = f"{text['title']}\n\n{text['description']}\n\n"

    # بناء الأزرار
    keyboard = []

    # أزرار الإصدارات (صفين)
    row1 = []
    row2 = []
    for i, version in enumerate(available_versions):
        is_selected = version in selected_versions
        button_text = f"{'✅' if is_selected else '☐'} {version}"
        callback_data = f"settings_toggle_version_{version}"

        if i < 2:  # أول صفين
            row1.append(InlineKeyboardButton(button_text, callback_data=callback_data))
        else:  # باقي الأزرار
            row2.append(InlineKeyboardButton(button_text, callback_data=callback_data))

    if row1:
        keyboard.append(row1)
    if row2:
        keyboard.append(row2)

    # أزرار التحكم
    keyboard.append([InlineKeyboardButton(text["select_all"], callback_data="settings_select_all_versions")])
    keyboard.append([InlineKeyboardButton(text["save"], callback_data="settings_save_versions")])
    keyboard.append([InlineKeyboardButton(text["back"], callback_data="show_channel_settings")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    # عرض الرسالة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def settings_toggle_version(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تبديل تحديد إصدار معين من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # استخراج الإصدار من callback_data
    version = query.data.split("_")[-1]  # settings_toggle_version_1.21+ -> 1.21+

    # تحديث الإصدارات
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    selected_versions = user_data.get('mc_versions', ['1.21+', '1.20+', '1.19+', '1.18+'])

    if version in selected_versions:
        selected_versions.remove(version)
    else:
        selected_versions.append(version)

    # حفظ التحديث
    user_data['mc_versions'] = selected_versions
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await manage_versions_from_settings(update, context)


async def settings_select_all_versions(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تحديد جميع إصدارات Minecraft من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # تحديد جميع الإصدارات
    all_versions = ['1.21+', '1.20+', '1.19+', '1.18+']

    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    user_data['mc_versions'] = all_versions
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await manage_versions_from_settings(update, context)


async def settings_save_versions(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """حفظ إصدارات Minecraft والعودة لإعدادات القناة"""
    query = update.callback_query
    await query.answer()

    lang = get_user_lang(query.from_user.id)

    success_texts = {
        "ar": "✅ تم حفظ إعدادات إصدارات Minecraft بنجاح",
        "en": "✅ Minecraft versions settings saved successfully"
    }

    await query.answer(success_texts.get(lang, success_texts['ar']), show_alert=True)

    # العودة لإعدادات القناة
    await show_channel_settings_for_user(update, context)


# --- دوال إدارة تنسيق الرسالة من الإعدادات ---

async def manage_format_from_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة إدارة تنسيق الرسالة من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # النصوص
    format_texts = {
        "ar": {
            "title": "🎨 <b>إدارة تنسيق الرسالة</b>",
            "description": "اختر التنسيق الذي تريد استخدامه لنشر المودات في قناتك:\n\n📌 يمكنك معاينة كل تنسيق قبل الاختيار.",
            "preview": "👁 معاينة التنسيق",
            "save": "💾 حفظ التغييرات",
            "back": "🔙 العودة لإعدادات القناة"
        },
        "en": {
            "title": "🎨 <b>Manage Message Format</b>",
            "description": "Choose the format you want to use for publishing mods in your channel:\n\n📌 You can preview each format before selecting.",
            "preview": "👁 Preview Format",
            "save": "💾 Save Changes",
            "back": "🔙 Back to Channel Settings"
        }
    }

    text = format_texts.get(lang, format_texts['ar'])

    # الحصول على التنسيق المختار حالياً
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_format = user_data.get('message_format', 'classic')

    # قائمة التنسيقات المتاحة
    format_options = {
        "classic": {
            "ar": "🔷 كلاسيكي",
            "en": "🔷 Classic"
        },
        "modern": {
            "ar": "✨ حديث",
            "en": "✨ Modern"
        },
        "elegant": {
            "ar": "💎 أنيق",
            "en": "💎 Elegant"
        },
        "minimal": {
            "ar": "🎯 بسيط",
            "en": "🎯 Minimal"
        },
        "gaming": {
            "ar": "🎮 ألعاب",
            "en": "🎮 Gaming"
        }
    }

    # بناء الرسالة
    message = f"{text['title']}\n\n{text['description']}\n\n"

    # بناء الأزرار
    keyboard = []

    # أزرار التنسيقات (صفين)
    row1 = []
    row2 = []
    row3 = []

    format_keys = list(format_options.keys())
    for i, format_key in enumerate(format_keys):
        is_selected = format_key == selected_format
        button_text = f"{'✅' if is_selected else '☐'} {format_options[format_key][lang]}"
        callback_data = f"settings_select_format_{format_key}"

        if i < 2:  # أول صفين
            row1.append(InlineKeyboardButton(button_text, callback_data=callback_data))
        elif i < 4:  # ثاني صفين
            row2.append(InlineKeyboardButton(button_text, callback_data=callback_data))
        else:  # باقي الأزرار
            row3.append(InlineKeyboardButton(button_text, callback_data=callback_data))

    if row1:
        keyboard.append(row1)
    if row2:
        keyboard.append(row2)
    if row3:
        keyboard.append(row3)

    # أزرار التحكم
    keyboard.append([InlineKeyboardButton(text["preview"], callback_data="settings_preview_format")])
    keyboard.append([InlineKeyboardButton(text["save"], callback_data="settings_save_format")])
    keyboard.append([InlineKeyboardButton(text["back"], callback_data="show_channel_settings")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    # عرض الرسالة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def settings_select_format(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تحديد تنسيق الرسالة من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # استخراج التنسيق من callback_data
    format_type = query.data.split("_")[-1]  # settings_select_format_classic -> classic

    # تحديث التنسيق
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    user_data['message_format'] = format_type
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await manage_format_from_settings(update, context)


async def settings_preview_format(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """معاينة تنسيق الرسالة المختار من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # الحصول على التنسيق المختار
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_format = user_data.get('message_format', 'classic')

    # بيانات مود تجريبية للمعاينة
    sample_mod = {
        "name": "Alylica's Dungeons",
        "description": "تهدف Alylica's Dungeons إلى مزج محتوى Minecraft Dungeons مع أسلوب وتدفق Minecraft الفانيليا، وتفتخر بمجموعة كبيرة من الأسلحة والأعداء والزعماء الأقوياء - إذا كنت من محبي Minecraft Dungeons وتلعب في البقاء على قيد الحياة، فقد تكون هذه الوظيفة الإضافية مناسبة لك!",
        "mc_version": "1.21.80 - 1.21.72",
        "download_link": "https://example.com/download",
        "category": "addons"
    }

    # إنشاء الرسالة المنسقة
    formatted_message = format_mod_message(sample_mod, selected_format, lang)

    # النصوص
    preview_texts = {
        "ar": {
            "title": "👁 <b>معاينة التنسيق</b>",
            "note": "📝 <i>هذه معاينة لكيفية ظهور المودات بالتنسيق المختار:</i>",
            "back": "🔙 العودة للاختيار"
        },
        "en": {
            "title": "👁 <b>Format Preview</b>",
            "note": "📝 <i>This is a preview of how mods will appear with the selected format:</i>",
            "back": "🔙 Back to Selection"
        }
    }
    text = preview_texts.get(lang, preview_texts['ar'])

    # تكوين الرسالة النهائية
    final_message = f"{text['title']}\n\n{text['note']}\n\n{'-'*30}\n\n{formatted_message}"

    keyboard = [[InlineKeyboardButton(text["back"], callback_data="setting_manage_format")]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # عرض المعاينة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, final_message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def settings_save_format(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """حفظ تنسيق الرسالة والعودة لإعدادات القناة"""
    query = update.callback_query
    await query.answer()

    lang = get_user_lang(query.from_user.id)

    success_texts = {
        "ar": "✅ تم حفظ إعدادات تنسيق الرسالة بنجاح",
        "en": "✅ Message format settings saved successfully"
    }

    await query.answer(success_texts.get(lang, success_texts['ar']), show_alert=True)

    # العودة لإعدادات القناة
    await show_channel_settings_for_user(update, context)


# --- دوال إدارة لغة القناة من الإعدادات ---

async def manage_language_from_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة إدارة لغة القناة من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    # النصوص
    language_texts = {
        "ar": {
            "title": "🌐 <b>إدارة لغة القناة</b>",
            "description": "اختر اللغة التي تريد استخدامها لنشر المودات في قناتك:\n\n📌 هذا سيحدد لغة الوصف والنصوص في المنشورات.",
            "arabic": "🇸🇦 العربية",
            "english": "🇺🇸 الإنجليزية",
            "save": "💾 حفظ التغييرات",
            "back": "🔙 العودة لإعدادات القناة"
        },
        "en": {
            "title": "🌐 <b>Manage Channel Language</b>",
            "description": "Choose the language you want to use for publishing mods in your channel:\n\n📌 This will determine the language of descriptions and texts in posts.",
            "arabic": "🇸🇦 Arabic",
            "english": "🇺🇸 English",
            "save": "💾 Save Changes",
            "back": "🔙 Back to Channel Settings"
        }
    }

    text = language_texts.get(lang, language_texts['ar'])

    # الحصول على لغة القناة المختارة حالياً
    user_channels = load_user_channels()
    user_data = user_channels.get(str(user_id), {})
    selected_channel_lang = user_data.get('channel_lang', lang)

    # بناء الرسالة
    message = f"{text['title']}\n\n{text['description']}\n\n"

    # بناء الأزرار
    keyboard = []

    # أزرار اللغات
    arabic_selected = selected_channel_lang == "ar"
    english_selected = selected_channel_lang == "en"

    keyboard.append([
        InlineKeyboardButton(
            f"{'✅' if arabic_selected else '☐'} {text['arabic']}",
            callback_data="settings_select_lang_ar"
        ),
        InlineKeyboardButton(
            f"{'✅' if english_selected else '☐'} {text['english']}",
            callback_data="settings_select_lang_en"
        )
    ])

    # أزرار التحكم
    keyboard.append([InlineKeyboardButton(text["save"], callback_data="settings_save_language")])
    keyboard.append([InlineKeyboardButton(text["back"], callback_data="show_channel_settings")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    # عرض الرسالة
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")


async def settings_select_language(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تحديد لغة القناة من إعدادات القناة"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user_id_str = str(user_id)

    # استخراج اللغة من callback_data
    channel_lang = query.data.split("_")[-1]  # settings_select_lang_ar -> ar

    # تحديث لغة القناة
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str, {})
    user_data['channel_lang'] = channel_lang
    user_channels[user_id_str] = user_data
    save_json_file(USER_CHANNELS_FILE, user_channels)

    # إعادة عرض القائمة
    await manage_language_from_settings(update, context)


async def settings_save_language(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """حفظ لغة القناة والعودة لإعدادات القناة"""
    query = update.callback_query
    await query.answer()

    lang = get_user_lang(query.from_user.id)

    success_texts = {
        "ar": "✅ تم حفظ إعدادات لغة القناة بنجاح",
        "en": "✅ Channel language settings saved successfully"
    }

    await query.answer(success_texts.get(lang, success_texts['ar']), show_alert=True)

    # العودة لإعدادات القناة
    await show_channel_settings_for_user(update, context)


async def delete_channel_confirm(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Asks for confirmation before deleting the user's channel data (unlinking)."""
    query = update.callback_query
    await query.answer()
    user_id = query.from_user.id
    lang = get_user_lang(user_id)

    texts = {
        "ar": {"confirm": "⚠️ <b>تأكيد الإلغاء</b>\n\nهل أنت متأكد من إلغاء ربط القناة؟ سيؤدي هذا إلى إيقاف النشر التلقائي لها ومسح إعداداتها من البوت.", "yes": "✅ نعم، إلغاء الربط", "no": "❌ تراجع"},
        "en": {"confirm": "⚠️ <b>Confirm Unlink</b>\n\nAre you sure you want to unlink the channel? This will stop automatic publishing and clear its settings from the bot.", "yes": "✅ Yes, unlink", "no": "❌ Cancel"}
    }
    text = texts.get(lang, texts['ar'])

    keyboard = [
        [
            InlineKeyboardButton(text["yes"], callback_data="setting_delete_channel_execute"),
            InlineKeyboardButton(text["no"], callback_data="show_channel_settings") # Back to settings
        ]
    ]
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, text["confirm"], is_photo=is_photo, reply_markup=InlineKeyboardMarkup(keyboard))


async def delete_channel_execute(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Deletes the user's channel data (unlinks the channel)."""
    query = update.callback_query
    await query.answer("⏳ جارٍ إلغاء الربط...", show_alert=False)
    user_id = query.from_user.id
    lang = get_user_lang(user_id)
    user_channels = load_user_channels()
    user_id_str = str(user_id)

    texts = {
        "ar": {"success": "✅ تم إلغاء ربط القناة ومسح إعداداتها بنجاح.", "error": "❌ خطأ: لم يتم العثور على بيانات القناة (ربما تم إلغاء ربطها بالفعل)."},
        "en": {"success": "✅ Channel unlinked and settings cleared successfully.", "error": "❌ Error: Channel data not found (perhaps already unlinked)."}
    }
    text = texts.get(lang, texts['ar'])

    if user_id_str in user_channels and isinstance(user_channels[user_id_str], dict):
        old_channel_id = user_channels[user_id_str].get("channel_id")
        # Instead of deleting the user, just remove channel-specific info
        user_channels[user_id_str]["channel_id"] = None
        user_channels[user_id_str]["active"] = False # Deactivate publishing
        user_channels[user_id_str]["last_publish_time"] = None
        # Keep lang, preview_enabled? Or reset them? Let's keep lang. Reset preview.
        # user_channels[user_id_str]["preview_enabled"] = False
        user_channels[user_id_str]["publish_interval"] = 60 # Reset interval to default

        save_json_file(USER_CHANNELS_FILE, user_channels) # Save changes

        logger.info(f"Unlinked channel (was {old_channel_id}) for user {user_id_str}")

        # Clean pending queue for the *old* channel ID
        if old_channel_id:
            clean_pending_queue(user_id_str, old_channel_id=str(old_channel_id), new_channel_id=None)

        is_photo = bool(query.message and query.message.photo)
        await update_ui_response(query, text["success"], is_photo=is_photo, reply_markup=None)
        # Show main menu after deletion
        await asyncio.sleep(1)
        await show_main_menu(update, context)
    else:
        logger.warning(f"Attempted to unlink channel data for user {user_id_str}, but none found or data invalid.")
        is_photo = bool(query.message and query.message.photo)
        await update_ui_response(query, text["error"], is_photo=is_photo, reply_markup=None)
        await asyncio.sleep(1)
        await show_main_menu(update, context)






async def help_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Displays the help menu."""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    lang = get_user_lang(user_id)
    texts = {
        "ar": {
            "contact_dev": "💬 تواصل مع المطور",
            "back": "🔙 العودة للقائمة الرئيسية",
            "help_title": "❓ مساعدة ودعم",
            "help_body": (
                "أهلاً بك في بوت نشر مودات ماينكرافت!\n\n"
                "🔹 <b>إدارة القناة والإعدادات:</b>\n"
                "   - لإضافة قناتك أو تغييرها (يجب أن يكون البوت مشرفاً فيها).\n"
                "   - للتحكم في الفاصل الزمني بين كل نشر مود.\n"
                "   - لتفعيل أو إيقاف النشر التلقائي.\n"
                "   - لتفعيل أو إيقاف استلام معاينة للمودات قبل نشرها.\n"
                "   - لإلغاء ربط قناتك الحالية.\n\n"
                "🔹 <b>تغيير اللغة:</b> للتبديل بين واجهة البوت العربية والإنجليزية.\n\n"
                "ℹ️ <b>ملاحظات هامة:</b>\n"
                "   - يتم جلب المودات الجديدة وفحصها بشكل دوري.\n"
                "   - يتلقى المسؤول معاينة للمودات الجديدة ليوافق عليها قبل إتاحتها للنشر.\n"
                "   - إذا واجهت مشكلة في النشر، تأكد أن البوت ما زال مشرفاً في قناتك ولديه صلاحية 'نشر الرسائل'.\n\n"
                f" للمزيد من المساعدة أو للإبلاغ عن مشكلة، يمكنك التواصل مع المطور:"
            )
        },
        "en": {
            "contact_dev": "💬 Contact Developer",
            "back": "🔙 Back to Main Menu",
            "help_title": "❓ Help & Support",
            "help_body": (
                "Welcome to the Minecraft Mods Publisher Bot!\n\n"
                "🔹 <b>Manage Channel & Settings:</b>\n"
                "   - Add or change your channel (bot must be an admin).\n"
                "   - Control the time interval between mod posts.\n"
                "   - Enable or disable automatic publishing.\n"
                "   - Enable or disable receiving previews before mods are published.\n"
                "   - Unlink your current channel.\n\n"
                "🔹 <b>Change Language:</b> Switch the bot interface between Arabic and English.\n\n"
                "ℹ️ <b>Important Notes:</b>\n"
                "   - New mods are fetched and checked periodically.\n"
                "   - The admin receives previews of new mods for approval before they become available for publishing.\n"
                "   - If you encounter publishing issues, ensure the bot is still an admin in your channel with 'Post Messages' permission.\n\n"
                f" For further assistance or to report an issue, you can contact the developer:"
            )
        }
    }
    text = texts.get(lang, texts['ar'])

    keyboard = []
    if ADMIN_USERNAME:
         keyboard.append([InlineKeyboardButton(text["contact_dev"], url=f"https://t.me/{ADMIN_USERNAME}")])
    keyboard.append([InlineKeyboardButton(text["back"], callback_data="main_menu")])
    reply_markup = InlineKeyboardMarkup(keyboard)

    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(
    query,
    f"<b>{text['help_title']}</b>\n\n{text['help_body']}",
    is_photo=is_photo,
    reply_markup=reply_markup,
    parse_mode="HTML" # <-- أضف parse_mode
)

# --- Publishing Logic ---

async def preview_mod_for_user_approval(mod: dict, user_id_str: str, context: ContextTypes.DEFAULT_TYPE):
    """
    Sends a specific mod preview to the admin, matching the final post format
    but prepended with admin info and using admin action buttons.
    """
    if not YOUR_CHAT_ID:
        logger.error("Cannot send admin preview: ADMIN_CHAT_ID not set.")
        return

    # Ensure mod ID exists before proceeding
    mod_id = mod.get('id')
    if mod_id is None:
        logger.error(f"Cannot send admin preview for user {user_id_str}: Mod ID is missing in mod data: {mod}")
        return

    # --- احصل على لغة المستخدم المستهدف لبناء المحتوى النهائي ---
    target_user_lang = get_user_lang(user_id_str)
    # -----------------------------------------------------------

    # --- <<<< التعديل يبدأ هنا >>>> ---

    # 1. احصل على الكابشن النهائي للمنشور باستخدام الدالة المساعدة
    #    (نتجاهل reply_markup الخاص بالتحميل)
    post_content = _build_mod_post_content(mod, target_user_lang)
    final_post_caption = post_content["caption"] # هذا هو النص الذي سيراه المستخدم

    # 2. جهز معلومات المسؤول التي ستُضاف قبل نص المنشور النهائي
    user_info_text = f"المستخدم <code>{user_id_str}</code>"
    try:
        user_chat = await context.bot.get_chat(user_id_str)
        if user_chat:
            user_info_text = f"<a href=\"tg://user?id={user_id_str}\">{html.escape(user_chat.full_name or user_id_str)}</a> (<code>{user_id_str}</code>)"
            if user_chat.username: user_info_text += f" @{html.escape(user_chat.username)}"
    except Exception as e:
        logger.warning(f"Could not fetch user info for {user_id_str} for admin preview: {e}")

    admin_header = (
        f"🔔 <b>اقتراح مود جديد لـ {user_info_text}</b>\n"
        f"   (حان وقت النشر في قناته - اللغة المستهدفة: <code>{target_user_lang}</code>)\n"
        f"   🆔 المود: {mod_id}\n"
        f"--- <i>شكل المنشور النهائي أدناه</i> ---\n\n" # فاصل لتوضيح أن ما يلي هو شكل المنشور
    )

    # 3. ادمج معلومات المسؤول مع نص المنشور النهائي
    admin_full_caption = admin_header + final_post_caption

    # 4. قم ببناء أزرار المسؤول (كما كانت سابقاً)
    keyboard = [
        [
            InlineKeyboardButton(f"✅ موافقة للمستخدم {user_id_str}", callback_data=f"admin_approve_user_{mod_id}_{user_id_str}"),
            InlineKeyboardButton(f"🚫 تخطي لهذا المستخدم", callback_data=f"admin_skip_user_{mod_id}_{user_id_str}")
        ]
    ]
    admin_reply_markup = InlineKeyboardMarkup(keyboard)

    # --- <<<< التعديل ينتهي هنا >>>> ---

    # --- إرسال المعاينة ---
    try:
        # Check if already awaiting approval (كما كان سابقاً)
        pending = load_pending_publications()
        is_awaiting = any(
            item.get("user_id") == user_id_str and
            item.get("mod_id") == mod_id and
            item.get("status") == "awaiting_admin_approval"
            for item in pending
        )
        if is_awaiting:
            logger.info(f"Skipping admin preview for mod {mod_id} / user {user_id_str} as it's already awaiting approval.")
            return

        # Add to pending state *before* sending preview (كما كان سابقاً)
        # استدعاء add_awaiting_admin_approval
        # ملاحظة: تأكد من أن دالة add_awaiting_admin_approval لا تزال تعمل بشكل صحيح
        # قد تحتاج لتمرير channel_id إذا كانت الدالة تتطلبه، أو تعديل الدالة لتجعله اختيارياً
        user_channels = load_user_channels()
        user_data_for_channel = user_channels.get(user_id_str)
        target_channel_id = user_data_for_channel.get("channel_id") if isinstance(user_data_for_channel, dict) else None

        success_add = add_awaiting_admin_approval(mod_id, user_id_str, target_channel_id)
        if not success_add:
            logger.warning(f"Failed to add mod {mod_id} / user {user_id_str} to awaiting status (maybe duplicate or other issue). Preview not sent.")
            return # لا ترسل المعاينة إذا فشلت الإضافة للحالة المعلقة

        # Send the message using the combined caption and admin buttons
        image_url = mod.get("image_url")
        if image_url:
            await context.bot.send_photo(
                chat_id=YOUR_CHAT_ID,
                photo=image_url,
                caption=admin_full_caption,     # استخدام الكابشن المدمج الجديد
                reply_markup=admin_reply_markup, # استخدام أزرار المسؤول
                parse_mode="HTML"
            )
        else:
            await context.bot.send_message(
                chat_id=YOUR_CHAT_ID,
                text=admin_full_caption,      # استخدام الكابشن المدمج الجديد
                reply_markup=admin_reply_markup, # استخدام أزرار المسؤول
                parse_mode="HTML",
                disable_web_page_preview=True # تعطيل معاينة الروابط في نص المنشور
            )
        logger.info(f"Sent enhanced admin preview for mod {mod_id} targeted at user {user_id_str}")

    # --- معالجة الأخطاء المحتملة عند الإرسال (كما كانت سابقاً، مع التأكد من حذف الحالة المعلقة عند الفشل) ---
    except BadRequest as e:
        logger.error(f"Failed to send admin preview for mod {mod_id} / user {user_id_str} (BadRequest: {e})")
        await send_notification(f"⚠️ فشل إرسال معاينة للمسؤول للمود {mod_id} للمستخدم {user_id_str} بسبب خطأ: {e.message}", context)
        remove_from_pending_by_user_mod(user_id_str, mod_id) # حاول الحذف إذا فشل الإرسال
    except TelegramError as e:
        logger.error(f"فشل إرسال معاينة المسؤول للمود {mod_id} للمستخدم {user_id_str}: {str(e)}")
        remove_from_pending_by_user_mod(user_id_str, mod_id) # حاول الحذف
    except Exception as e:
        logger.error(f"فشل إرسال معاينة المسؤول للمود {mod_id} للمستخدم {user_id_str} (Unexpected): {str(e)}", exc_info=True)
        remove_from_pending_by_user_mod(user_id_str, mod_id) # حاول الحذف

async def preview_mod_before_publish(mod, context: ContextTypes.DEFAULT_TYPE):
    """إرسال معاينة خاصة للمسؤول فقط (استخدام محدود الآن)."""
    if not YOUR_CHAT_ID:
         logger.error("Cannot send admin preview: ADMIN_CHAT_ID not set.")
         return

    admin_caption = f"🔔 <b>معاينة مسؤول للمود الجديد</b> (ID: {mod['id']})\n\n"
    admin_caption += f"<b>{html.escape(mod['title'])}</b>\n\n"

    # --- اختيار وصف (مثلاً، العربي كافتراضي للمعاينة العامة) ---
    description_obj = mod.get('description', {})
    description_text = description_obj.get('ar', description_obj.get('en', '[Description unavailable]'))
    # ----------------------------------------------------

    max_desc_len = 300
    if len(description_text) > max_desc_len:
        description_display = html.escape(description_text[:max_desc_len]) + "..."
    else:
        description_display = html.escape(description_text)

    admin_caption += f"{description_display}\n\n"
    admin_caption += f"<a href=\"{mod['download_url']}\">🔗 رابط التحميل</a>"

    # الأزرار القديمة للموافقة العامة قد تحتاج لإعادة نظر في وظيفتها الآن
    keyboard = [
        [
            InlineKeyboardButton("✅ موافقة (عام)", callback_data=f"admin_publish_{mod['id']}"), # أصبح معناه مختلف الآن
            InlineKeyboardButton("🚫 تخطي (عام)", callback_data=f"admin_skip_{mod['id']}")
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    try:
        # Check if this mod was *already* processed by admin (e.g., if bot restarted during processing)
        admin_processed = load_admin_processed_mods()
        if mod['id'] in admin_processed:
            logger.info(f"Skipping admin preview for mod {mod['id']} as it was already processed.")
            return

        if mod.get("image_url"):
            await context.bot.send_photo(
                chat_id=YOUR_CHAT_ID,
                photo=mod["image_url"],
                caption=admin_caption,
                reply_markup=reply_markup,
                parse_mode="HTML"
            )
        else:
            await context.bot.send_message(
                chat_id=YOUR_CHAT_ID,
                text=admin_caption,
                reply_markup=reply_markup,
                parse_mode="HTML"
            )
        logger.info(f"Sent admin preview for mod {mod['id']}")
    except BadRequest as e:
         # Handle potential errors like invalid image URL or admin blocked the bot
         logger.error(f"Failed to send admin preview for mod {mod['id']} (BadRequest: {e})")
         await send_notification(f"⚠️ فشل إرسال معاينة المسؤول للمود {mod['id']} بسبب خطأ: {e.message}", context)
         # Mark as processed anyway to avoid retry loops? Or maybe not if it's temporary?
         # save_admin_processed_mod(mod['id'])
    except TelegramError as e:
        logger.error(f"فشل إرسال معاينة المسؤول للمود {mod['id']}: {str(e)}")
        # Don't notify admin about temporary network errors usually
    except Exception as e:
        logger.error(f"فشل إرسال معاينة المسؤول للمود {mod['id']} (Unexpected): {str(e)}", exc_info=True)
        await send_notification(f"⚠️ فشل إرسال معاينة المسؤول للمود {mod['id']} بسبب خطأ غير متوقع.", context)


# --- Helper function to update pending status ---
def update_pending_status(user_id: str, mod_id: int, new_status: str):
    """Updates the status of a specific pending item and its timestamp.""" # <-- تعديل الوصف
    pending = load_pending_publications()
    user_id_str = str(user_id)
    updated = False
    now_iso = datetime.now(timezone.utc).isoformat() # وقت التحديث

    for item in pending:
        if item.get("user_id") == user_id_str and item.get("mod_id") == mod_id:
            if item.get("status") != new_status:
                item["status"] = new_status
                # --- تعديل: تحديث وقت تغيير الحالة ---
                item["status_update_time"] = now_iso
                # --- إضافة وقت الاقتراح إذا لم يكن موجوداً (احتياطي) ---
                if "proposal_time" not in item:
                    item["proposal_time"] = now_iso
                # -----------------------------------------------
                updated = True
                logger.info(f"Updated pending status for mod {mod_id}/user {user_id_str} to '{new_status}' at {now_iso}")
            else:
                 logger.debug(f"Pending status for mod {mod_id}/user {user_id_str} is already '{new_status}'. No change needed.")
            break
    else:
        logger.warning(f"Could not find pending item for mod {mod_id}/user {user_id_str} to update status to '{new_status}'.")
        return False

    if updated:
        save_pending_publications(pending)

    return updated

async def handle_preview_response(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles admin choices on the mod preview (both general and user-specific)."""
    query = update.callback_query
    await query.answer() # Acknowledge button press

    if str(query.from_user.id) != YOUR_CHAT_ID:
        await query.answer("❌ صلاحيات غير كافية!", show_alert=True)
        return

    data = query.data
    is_photo = bool(query.message and query.message.photo) # Check if original message was photo

    # --- NEW: Handle User-Specific Approval/Skip ---
    if data.startswith("admin_approve_user_") or data.startswith("admin_skip_user_"):
        try:
            # Pattern: admin_action_user_<mod_id>_<user_id>
            parts = data.split("_")
            action = parts[1] # approve or skip
            mod_id = int(parts[3])
            target_user_id = parts[4]
        except (ValueError, IndexError) as e:
            logger.error(f"Invalid callback data in user-specific admin response: {data} - {e}")
            await update_ui_response(query, "❌ خطأ في بيانات الاستدعاء.", is_photo=is_photo)
            return

        logger.info(f"Admin action '{action}' received for mod {mod_id} targeted at user {target_user_id}")

        # Check if the item exists in pending with 'awaiting_admin_approval' status
        pending = load_pending_publications()
        matching_item_index = -1
        for i, item in enumerate(pending):
            # Ensure we are acting on the correct item state
            if (item.get("user_id") == target_user_id and
                item.get("mod_id") == mod_id and
                item.get("status") == "awaiting_admin_approval"):
                matching_item_index = i
                break

        original_message_content = query.message.caption if is_photo else query.message.text
        admin_confirm_msg = original_message_content # Start with original content

        if matching_item_index == -1:
            logger.warning(f"Admin responded ({action}) for mod {mod_id}/user {target_user_id}, but no matching 'awaiting_admin_approval' entry found.")
            await query.answer("⚠️ لم يتم العثور على طلب الموافقة هذا (ربما تمت معالجته بالفعل أو تم تخطيه).", show_alert=True)
            # Avoid editing if message already indicates processing
            if "النتيجة لـ" not in original_message_content and "Result for" not in original_message_content:
                 try: await update_ui_response(query, original_message_content + "\n\n---\n✅ (تمت معالجته مسبقاً)", is_photo=is_photo, reply_markup=None)
                 except Exception: pass
            return

        # --- Process the action ---
        admin_confirm_msg += f"\n\n--- <b>النتيجة لـ المستخدم {target_user_id}</b> ---"

        if action == "approve":
            # Get user data to check preview setting
            user_channels = load_user_channels()
            user_data = user_channels.get(target_user_id)
            user_preview_enabled = isinstance(user_data, dict) and user_data.get("preview_enabled", False)
            user_lang = get_user_lang(target_user_id) # Get user lang for potential user preview

            # --- User Preview Logic ---
            if user_preview_enabled:
                logger.info(f"Admin approved mod {mod_id} for user {target_user_id}. User has preview enabled. Sending preview to user.")
                admin_confirm_msg += f"\n⏳ تمت الموافقة. سيتم إرسال معاينة للمستخدم {target_user_id} لأنه مفعّل للمعاينة."
                # Update status in pending file to 'awaiting_user_approval'
                status_updated = update_pending_status(target_user_id, mod_id, "awaiting_user_approval")
                if not status_updated:
                     logger.error(f"Failed to update pending status to 'awaiting_user_approval' for {mod_id}/{target_user_id}")
                     admin_confirm_msg += "\n⚠️ خطأ في تحديث حالة الانتظار للمستخدم."
                     # Attempt to clean up - remove the original 'awaiting_admin_approval' entry if status update failed
                     remove_from_pending_by_user_mod(target_user_id, mod_id)
                else:
                    # Find the mod data again to send to the user
                    # *** START CORRECTION ***
                    # mods = load_mods(user_lang) # <-- الخطأ كان هنا
                    mods = load_mods() # <-- الصحيح: تحميل جميع المودات
                    mod_to_preview = next((m for m in mods if m.get('id') == mod_id), None) # البحث بالـ ID
                    # *** END CORRECTION ***

                    if mod_to_preview:
                        # Send preview to user asynchronously
                        asyncio.create_task(send_single_user_preview(context, target_user_id, mod_to_preview, user_lang))
                        logger.info(f"Scheduled user preview task for mod {mod_id} / user {target_user_id}")
                    else:
                        logger.error(f"Could not retrieve mod data {mod_id} from loaded mods to send user preview for {target_user_id}")
                        admin_confirm_msg += f"\n⚠️ خطأ: لم يتم العثور على بيانات المود {mod_id} لإرسالها للمستخدم."
                        # Clean up the pending item since we can't proceed
                        remove_from_pending_by_user_mod(target_user_id, mod_id)

            else: # User preview is disabled
                # --- Direct Publish Logic ---
                logger.info(f"Admin approved mod {mod_id} for user {target_user_id}. User preview disabled. Attempting direct publication.")
                # Remove the 'awaiting_admin_approval' item from pending first
                item_removed = False
                if matching_item_index != -1 and pending[matching_item_index].get("status") == "awaiting_admin_approval":
                    del pending[matching_item_index]
                    save_pending_publications(pending)
                    item_removed = True
                    logger.info(f"Removed 'awaiting_admin_approval' item for mod {mod_id}/user {target_user_id} before direct publish.")
                else:
                     logger.warning(f"Could not find or remove 'awaiting_admin_approval' item for mod {mod_id}/user {target_user_id} before direct publish attempt.")

                if item_removed:
                    # *** START CORRECTION (Ensure publish_single_mod is called correctly) ***
                    # No change needed here, publish_single_mod takes mod_id
                    success = await publish_single_mod(mod_id, target_user_id, context)
                    # *** END CORRECTION ***
                    if success:
                        admin_confirm_msg += f"\n✅ تمت الموافقة والنشر مباشرة بنجاح للمستخدم {target_user_id}."
                    else:
                        admin_confirm_msg += f"\n❌ تمت الموافقة ولكن فشل النشر المباشر للمستخدم {target_user_id} (راجع السجلات)."
                        save_published_mod(target_user_id, mod_id) # Mark as processed even on failure
                else:
                     admin_confirm_msg += f"\n⚠️ فشل حذف طلب الموافقة السابق، تم إلغاء النشر المباشر للمستخدم {target_user_id}."


            # Update the admin's message AFTER processing approval logic
            await update_ui_response(query, admin_confirm_msg, is_photo=is_photo, reply_markup=None)

        elif action == "skip":
            logger.info(f"Admin skipped mod {mod_id} for user {target_user_id}.")
            # Remove the 'awaiting_admin_approval' item from pending queue
            removed = remove_from_pending_by_user_mod(target_user_id, mod_id)
            if removed:
                 logger.debug(f"Removed pending item for skipped mod {mod_id}/user {target_user_id}")
            else:
                 logger.warning(f"Could not remove pending item for skipped mod {mod_id}/user {target_user_id} (might have been removed already).")

            admin_confirm_msg += f"\n🚫 تم تخطي نشر المود {mod_id} للمستخدم {target_user_id}."
            # Mark as processed for this user so it's not proposed again soon
            save_published_mod(target_user_id, mod_id) # Treat skip as 'processed'
            await update_ui_response(query, admin_confirm_msg, is_photo=is_photo, reply_markup=None)

        return # Handled user-specific action

    # --- Handle General Approval/Skip (Keep as potentially deprecated but functional) ---
    elif data.startswith("admin_publish_") or data.startswith("admin_skip_"):
        try:
            action_general, mod_id_str = data.split("_", 2)[1:]
            mod_id = int(mod_id_str)
        except (ValueError, IndexError) as e:
            logger.error(f"Invalid callback data in general admin response: {data} - {e}")
            await update_ui_response(query, "❌ خطأ في بيانات الاستدعاء.", is_photo=is_photo)
            return

        admin_processed = load_admin_processed_mods()
        if mod_id in admin_processed:
             await query.answer(f"⚠️ هذا المود ({mod_id}) تمت معالجته مسبقاً.", show_alert=True)
             # Update UI to reflect it's already processed if message doesn't show it yet
             confirm_msg = query.message.caption if is_photo else query.message.text
             if "النتيجة (عام)" not in confirm_msg:
                 try: await update_ui_response(query, confirm_msg + "\n\n---\n✅ (تمت معالجته مسبقاً)", is_photo=is_photo, reply_markup=None)
                 except Exception: pass
             return # Stop further processing

        # Mark as globally processed
        save_admin_processed_mod(mod_id)
        confirm_msg = query.message.caption if is_photo else query.message.text
        confirm_msg += "\n\n--- <b>النتيجة (عام)</b> ---"

        if action_general == "publish":
            logger.warning(f"Admin used GENERAL publish action for mod {mod_id}. This only marks it globally processed.")
            confirm_msg += f"\n✅ تم تعليم المود {mod_id} كـ'معالج عالمياً'. (لن يؤثر مباشرة على المستخدمين)."

        elif action_general == "skip":
            logger.info(f"Admin used GENERAL skip action for mod {mod_id}. Marking globally processed.")
            confirm_msg += f"\n🚫 تم تخطي المود {mod_id} بشكل عام وتعليمه كـ'معالج عالمياً'."

        await update_ui_response(query, confirm_msg, is_photo=is_photo, reply_markup=None)

    else:
        logger.warning(f"Unknown action in admin preview response: {data}")
        await update_ui_response(query, "❓ إجراء غير معروف.", is_photo=is_photo)

async def handle_user_block_response(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles user pressing the 'Block' button on a mod preview."""
    query = update.callback_query
    await query.answer() # Acknowledge

    user_id = query.from_user.id
    user_id_str = str(user_id)
    lang = get_user_lang(user_id)
    is_photo = bool(query.message and query.message.photo)

    try:
        # Pattern: user_block_<mod_id>
        mod_id_str = query.data.split("_")[-1]
        mod_id = int(mod_id_str)
    except (ValueError, IndexError) as e:
        logger.error(f"Invalid callback data in user block response: {query.data} - {e}")
        await update_ui_response(query, "❌ خطأ.", is_photo=is_photo)
        return

    logger.info(f"User {user_id_str} requested to block mod {mod_id}")

    # --- Check if item is awaiting user approval (should be) ---
    removed = remove_from_pending_by_user_mod(user_id_str, mod_id)
    if not removed:
        logger.warning(f"User {user_id_str} tried to block mod {mod_id}, but it wasn't found in pending ('awaiting_user_approval' expected).")
        # Check if already blocked
        blocked_mods = load_user_blocked_mods()
        if mod_id in blocked_mods.get(user_id_str, []):
             await query.answer("لقد قمت بحظر هذا المود بالفعل." if lang == 'ar' else "You have already blocked this mod.", show_alert=True)
             return
        # else: # Maybe processed differently? Treat as success anyway.
        #    pass

    # Add to blocked list
    save_user_blocked_mod(user_id_str, mod_id)

    # Also add to general processed list for this user
    save_published_mod(user_id_str, mod_id)

    # Save feedback as 'rejected' (or maybe a specific 'blocked' status if needed)
    save_user_feedback(user_id_str, mod_id, False) # Treat block as a rejection in feedback

    # Update user's UI
    confirm_texts = {
        "ar": {"blocked": "🚫 تم حظر هذا المود بنجاح. لن يتم اقتراحه أو نشره لك مرة أخرى."},
        "en": {"blocked": "🚫 This mod has been blocked successfully. It will not be proposed or published to you again."}
    }
    text = confirm_texts.get(lang, confirm_texts['ar'])
    result_message = text["blocked"]

    original_content = query.message.caption if is_photo else query.message.text
    final_content = original_content + f"\n\n---\n{result_message}"

    await update_ui_response(query, final_content, is_photo=is_photo, reply_markup=None) # Remove buttons


async def handle_user_preview_response(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles user Approve/Reject choices on their mod preview."""
    query = update.callback_query
    await query.answer() # Acknowledge

    user_id = query.from_user.id
    user_id_str = str(user_id)
    lang = get_user_lang(user_id)
    is_photo = bool(query.message and query.message.photo) # Check message type

    try:
        # Pattern: user_approve_<mod_id> or user_reject_<mod_id>
        action_type, mod_id_str = query.data.split("_")[1:] # e.g., user_approve_123 -> [approve, 123]
        mod_id = int(mod_id_str)
    except (ValueError, IndexError) as e:
        logger.error(f"Invalid callback data in user preview response: {query.data} - {e}")
        await update_ui_response(query, "❌ خطأ في بيانات الاستدعاء.", is_photo=is_photo)
        return

    is_approved = action_type == "approve"

    # --- Check if item is awaiting user approval in pending list ---
    pending = load_pending_publications()
    item_found_awaiting_user = False
    item_index_to_remove = -1 # Store index to remove later if needed

    for i, item in enumerate(pending):
        if (item.get("user_id") == user_id_str and
            item.get("mod_id") == mod_id and
            item.get("status") == "awaiting_user_approval"):
            item_found_awaiting_user = True
            item_index_to_remove = i
            logger.debug(f"Found mod {mod_id} for user {user_id_str} awaiting user approval.")
            break

    original_content = query.message.caption if is_photo else query.message.text
    final_content = original_content # Start with original content

    # --- Prevent double processing / Handle case where item not found ---
    if not item_found_awaiting_user:
        logger.warning(f"User {user_id_str} responded ({action_type}) for mod {mod_id}, but no matching 'awaiting_user_approval' entry found in pending queue.")
        # Check if maybe already processed (approved/rejected/blocked)
        feedback = load_user_feedback()
        blocked_list = load_user_blocked_mods().get(user_id_str, [])
        mods_status_list = load_user_mods_status().get(user_id_str, [])
        feedback_key = f"{user_id_str}_{mod_id}"

        final_status_msg = "لقد قمت بمعالجة هذا المود مسبقاً." if lang == 'ar' else "You have already processed this mod."
        if mod_id in blocked_list:
            final_status_msg = "لقد قمت بحظر هذا المود مسبقاً." if lang == 'ar' else "You have already blocked this mod."
        elif feedback_key in feedback: # Check feedback file as another source
            prev_approved = feedback[feedback_key]
            final_status_msg = f"لقد {'وافقت' if prev_approved else 'رفضت'} على هذا المود مسبقاً." if lang == 'ar' else f"You already {'approved' if prev_approved else 'rejected'} this mod."
        elif mod_id in mods_status_list: # Check if it's in the general published/rejected status list
             final_status_msg = "لقد تم نشر هذا المود أو رفضه مسبقاً." if lang == 'ar' else "This mod was already published or rejected."


        await query.answer(final_status_msg, show_alert=True)
        # Update UI only if the status message isn't already present
        if "مسبقاً" not in original_content and "already" not in original_content and "rejected" not in original_content and "blocked" not in original_content:
            try:
                await update_ui_response(query, final_content + f"\n\n---\n{final_status_msg}", is_photo=is_photo, reply_markup=None)
            except Exception as edit_err:
                 logger.warning(f"Could not edit message to show already processed status: {edit_err}")
        return
    # --- End Check ---

    # If found, remove it from the pending list now that the user has responded
    if item_index_to_remove != -1:
        del pending[item_index_to_remove]
        save_pending_publications(pending)
        logger.debug(f"Removed mod {mod_id} / user {user_id_str} from pending queue after user response.")

    # Save user feedback (Approve/Reject) regardless of publishing success
    save_user_feedback(user_id_str, mod_id, is_approved)
    logger.info(f"User {user_id_str} responded: {'Approved' if is_approved else 'Rejected'} mod {mod_id}")

    # --- Define Confirmation Texts ---
    confirm_texts = {
        "ar": {
            "approved_success": "شكراً لموافقتك ✅! تم نشر المود بنجاح في قناتك.",
            "approved_failed": "شكراً لموافقتك ✅. ⚠️ لكن حدث خطأ أثناء محاولة النشر الفوري للقناة (راجع السجلات).",
            "rejected": "شكراً لك! تم الرفض ❌ لن يتم نشر هذا المود.",
            "error": "حدث خطأ، يرجى المحاولة لاحقاً." # General error, less likely now
        },
        "en": {
            "approved_success": "Thanks for approving ✅! The mod was published successfully to your channel.",
            "approved_failed": "Thanks for approving ✅. ⚠️ However, an error occurred while trying to publish to the channel (check logs).",
            "rejected": "Thank you! Rejected ❌ This mod will not be published.",
            "error": "An error occurred, please try again later."
        }
    }
    text = confirm_texts.get(lang, confirm_texts['ar'])
    result_message = ""

    # --- Handle Approve/Reject Actions ---
    if is_approved:
        logger.info(f"User {user_id_str} approved mod {mod_id}. Attempting immediate publication.")
        # Attempt to publish immediately
        publish_success = await publish_single_mod(mod_id, user_id_str, context)

        if publish_success:
            result_message = text["approved_success"]
            # Note: publish_single_mod already updates last_publish_time and adds to user_mods_status
        else:
            result_message = text["approved_failed"]
            logger.error(f"Publish failed for mod {mod_id} to user {user_id_str} channel after user approval.")
            # Mark as processed anyway to avoid retrying this specific failed mod proposal cycle immediately
            save_published_mod(user_id_str, mod_id)

    else: # User Rejected
        result_message = text["rejected"]
        logger.info(f"User {user_id_str} rejected mod {mod_id}. Marking as processed/rejected.")
        # Mark as processed/rejected for this user
        save_published_mod(user_id_str, mod_id)

    # --- Update User's Message ---
    final_content += f"\n\n---\n{result_message}"
    await update_ui_response(query, final_content, is_photo=is_photo, reply_markup=None) # Remove buttons

    # --- REMOVED: Sending Next Preview Immediately ---
    # The logic to send the next preview is removed. The main job `check_users_and_propose_mods`
    # will handle proposing the next mod when the user's schedule is due again.


# --- إضافة في بداية الملف ---
import random
# --------------------------

# --- تعديل: تعريف أنواع المودات والإيموجيات (يمكن وضعها في بداية الملف أو قبل الدالة) ---
# -*- coding: utf-8 -*-
import random
import html
from telegram import InlineKeyboardButton, InlineKeyboardMarkup

# --- تأكد من تعريف هذه الثوابت في أعلى الملف ---
MOD_TYPES_DISPLAY = {
    "ar": { "mod": "مود جديد", "shader": "شادر جديد", "map": "ماب جديد", "texture_pack": "تكستر باك جديد", "addon": "أدون جديد", "unknown": "محتوى جديد" },
    "en": { "mod": "New Mod", "shader": "New Shader", "map": "New Map", "texture_pack": "New Texture Pack", "addon": "New Addon", "unknown": "New Content" }
}
RANDOM_EMOJIS = ["✨", "🔥", "🚀", "💡", "🎮", "🌍", "🧱", "💎", "⚔️", "🎯", "🎉", "💡", "🧩", "🎨", "🗺️", "🔮", "🎋", "🌪️", "⚡", "🍉", "☀️", "❄️"]
INSTALL_GUIDE_URL = "https://cvkrtjvrg.blogspot.com/p/blog-page.html"
# -------------------------------------------------

# --- الدالة المساعدة الجديدة ---
def _build_mod_post_content(mod_data: dict, lang: str, user_id_str: str = None) -> dict:
    """
    تبني الكابشن وزر التحميل لمنشور المود بالشكل النهائي.
    Args:
        mod_data: بيانات المود
        lang: لغة المستخدم
        user_id_str: معرف المستخدم (لاستخراج تنسيق الرسالة المفضل)
    Returns:
        dict: يحتوي على 'caption' و 'reply_markup'.
    """
    try:
        # --- الحصول على تنسيق الرسالة ولغة القناة المفضلة للمستخدم ---
        message_format = "classic"  # افتراضي
        channel_lang = lang  # افتراضي
        if user_id_str:
            user_channels = load_user_channels()
            user_data = user_channels.get(user_id_str, {})
            message_format = user_data.get('message_format', 'classic')
            channel_lang = user_data.get('channel_lang', lang)

        # --- استخراج البيانات من المود ---
        description_obj = mod_data.get('description', {})
        description_text = description_obj.get(channel_lang, description_obj.get('en', '[Description unavailable]'))
        mod_title = mod_data.get('title', '[Untitled Mod]')
        download_url = mod_data.get('download_url')
        version = mod_data.get("version")
        mod_type_key = mod_data.get("mod_type", "unknown").lower()
        minecraft_edition_raw = mod_data.get("for")

        # --- تحضير بيانات المود للتنسيق ---
        mod_for_formatting = {
            'name': mod_title,
            'description': description_text,
            'mc_version': version or minecraft_edition_raw or 'غير محدد',
            'download_link': download_url or '',
            'category': mod_type_key
        }

        # --- استخدام دالة التنسيق الجديدة مع لغة القناة ---
        caption = format_mod_message(mod_for_formatting, message_format, channel_lang)

        # --- بناء زر التحميل (إذا وجد الرابط) ---
        buttons = []
        if download_url:
            download_button_text = "تحميل ⬇️" if channel_lang == "ar" else "⬇️ Download"
            buttons.append([InlineKeyboardButton(download_button_text, url=download_url)])
        reply_markup = InlineKeyboardMarkup(buttons) if buttons else None

        return {"caption": caption, "reply_markup": reply_markup}

    except Exception as e:
        logger.error(f"_build_mod_post_content: Error building content for mod {mod_data.get('id', 'N/A')}: {e}", exc_info=True)
        # Return basic info on error to prevent crash
        fallback_caption = f"<b>{html.escape(mod_data.get('title', '[Error]'))}</b>\n[Error building details]"
        return {"caption": fallback_caption, "reply_markup": None}

# --- بقية الكود الخاص بك ... ---
# ---------------------------------------------------------------------------------------

async def publish_single_mod(mod_id: int, user_id_str: str, context: ContextTypes.DEFAULT_TYPE) -> bool:
    """Publishes a specific mod to a specific user's channel. Includes permission check, mod type classification, edition, and install link. Sends failure notifications to the user.""" # <-- تم تحديث الوصف
    user_channels = load_user_channels()
    user_data = user_channels.get(user_id_str)

    if not isinstance(user_data, dict):
        logger.error(f"publish_single_mod: Invalid user data for {user_id_str}. Cannot publish.")
        return False

    channel_id = user_data.get("channel_id")
    lang = user_data.get("lang", "ar") # Default to Arabic

    if not channel_id:
        logger.error(f"publish_single_mod: No channel ID for user {user_id_str}. Cannot publish.")
        return False

    # === Final Permission Check ===
    # Ensures we check right before posting
    permissions_ok = await check_channel_permissions(context.bot, channel_id, user_id_str)
    if not permissions_ok:
         logger.warning(f"Final permission check failed for channel {channel_id} (User: {user_id_str}) just before publishing mod {mod_id}.")
         # check_channel_permissions already notifies the user
         return False
    # =============================

    all_mods = load_mods()
    mod_data = next((m for m in all_mods if m.get("id") == mod_id), None)

    if not mod_data:
        logger.error(f"publish_single_mod: Mod data for ID {mod_id} not found in main mods file ({MODS_FILE}) for user {user_id_str}. Cannot publish.")
        # --- إرسال إشعار للمستخدم بأن بيانات المود غير موجودة ---
        error_texts_mod_not_found = {
            "ar": f"⚠️ تعذر نشر المود (ID: {mod_id}) لأن بياناته غير موجودة.",
            "en": f"⚠️ Could not publish mod (ID: {mod_id}) because its data was not found."
        }
        try:
            await context.bot.send_message(
                chat_id=user_id_str,
                text=error_texts_mod_not_found.get(lang, error_texts_mod_not_found['ar'])
            )
        except Exception as notify_err:
            logger.error(f"Failed to notify user {user_id_str} about missing mod data {mod_id}: {notify_err}")
        # ------------------------------------------------------
        return False # مهم: لا تكمل إذا لم تجد المود

    # --- محاولة النشر ---
    try:
        post_content = _build_mod_post_content(mod_data, lang, user_id_str)
        caption = post_content["caption"]
        reply_markup = post_content["reply_markup"]
        image_url = mod_data.get("image_url")

        if image_url:
            await context.bot.send_photo(
                chat_id=channel_id,
                photo=image_url,
                caption=caption,
                parse_mode="HTML",
                reply_markup=reply_markup
            )
        else:
            await context.bot.send_message(
                chat_id=channel_id,
                text=caption,
                parse_mode="HTML",
                reply_markup=reply_markup,
                disable_web_page_preview=True
            )

        logger.info(f"Successfully published mod {mod_id} (lang '{lang}') to user {user_id_str} (channel {channel_id})")

        # --- تحديث حالة النشر والوقت ---
        save_published_mod(user_id_str, mod_id)
        now_iso = datetime.now(timezone.utc).isoformat()
        update_success = update_publish_settings(int(user_id_str), last_publish_time_str=now_iso)
        if not update_success:
             logger.warning(f"Failed to update last publish time for user {user_id_str} after successful publish.")
        # -------------------------------

        # --- جدولة المود التالي (إذا لم تكن المعاينة مفعلة) ---
        # Removed for brevity, assume it's handled correctly by check_users_and_propose_mods

        return True

    # --- <<<< تعديل معالجة الأخطاء لإعلام المستخدم >>>> ---
    except Forbidden as e:
        logger.error(f"Publish failed (Forbidden) for mod {mod_id} to channel {channel_id} (User: {user_id_str}): {e}")
        error_key = "forbidden"
        error_detail = str(e)
        # تحديد سبب المنع بشكل أدق إن أمكن
        if "bot was kicked" in error_detail.lower() or "user is deactivated" in error_detail.lower():
            error_key = "bot_kicked"
        elif "bot is not a member" in error_detail.lower():
             error_key = "not_member"
        elif "chat not found" in error_detail.lower() or "peer id invalid" in error_detail.lower():
             error_key = "chat_not_found"
        elif "have no rights to send a message" in error_detail.lower():
             error_key = "no_post_rights"

        error_texts = {
            "ar": {
                "forbidden": f"❌ فشل النشر في القناة {channel_id} للمود (ID: {mod_id}) بسبب مشكلة في الصلاحيات (ربما تم حظر البوت أو ليس لديه صلاحية النشر).",
                "bot_kicked": f"❌ فشل النشر في القناة {channel_id} للمود (ID: {mod_id}) لأنه تم طرد البوت منها.",
                "not_member": f"❌ فشل النشر في القناة {channel_id} للمود (ID: {mod_id}) لأن البوت ليس عضواً فيها.",
                "chat_not_found": f"❌ فشل النشر في القناة {channel_id} للمود (ID: {mod_id}) لأنه تعذر العثور عليها (ربما حُذفت أو تغير معرفها).",
                "no_post_rights": f"❌ فشل النشر في القناة {channel_id} للمود (ID: {mod_id}) لأنه لا يملك صلاحية 'إرسال الرسائل'."
            },
            "en": {
                "forbidden": f"❌ Failed to publish mod (ID: {mod_id}) to channel {channel_id} due to permission issues (Bot might be blocked or lacks posting rights).",
                "bot_kicked": f"❌ Failed to publish mod (ID: {mod_id}) to channel {channel_id} because the bot was kicked.",
                "not_member": f"❌ Failed to publish mod (ID: {mod_id}) to channel {channel_id} because the bot is not a member.",
                "chat_not_found": f"❌ Failed to publish mod (ID: {mod_id}) to channel {channel_id} because the channel was not found (maybe deleted or ID changed).",
                "no_post_rights": f"❌ Failed to publish mod (ID: {mod_id}) to channel {channel_id} because it lacks 'Send Messages' permission."
            }
        }
        user_message = error_texts.get(lang, error_texts['ar']).get(error_key, error_texts.get(lang, error_texts['ar'])['forbidden'])
        try:
            await context.bot.send_message(chat_id=user_id_str, text=user_message)
        except Exception as notify_err:
            logger.error(f"Failed to notify user {user_id_str} about publishing error (Forbidden): {notify_err}")
        save_published_mod(user_id_str, mod_id) # علم المود كمعالج لهذا المستخدم حتى لو فشل النشر
        return False

    except BadRequest as e:
        logger.error(f"Publish failed (BadRequest) for mod {mod_id} to channel {channel_id} (User: {user_id_str}): {e}")
        error_key = "bad_request"
        error_detail = str(e).lower()
        # تحليل أخطاء BadRequest الشائعة
        if "chat not found" in error_detail or "peer id invalid" in error_detail:
             error_key = "chat_not_found_br" # تمييزه عن Forbidden
        elif "message caption is too long" in error_detail:
             error_key = "caption_too_long"
        elif "description is too long" in error_detail: # قد يأتي بهذا الشكل أيضاً
             error_key = "caption_too_long"
        elif "wrong file identifier" in error_detail or "failed to get http url content" in error_detail:
             error_key = "bad_image"

        texts = {
            "ar": {
                "bad_request": f"❌ فشل النشر في القناة {channel_id} للمود (ID: {mod_id}) بسبب خطأ في الطلب. ({e.message})",
                "chat_not_found_br": f"❌ فشل النشر في القناة {channel_id} للمود (ID: {mod_id}) لأنه تعذر العثور عليها (ربما حُذفت أو تغير معرفها).",
                "caption_too_long": f"❌ فشل نشر المود (ID: {mod_id}) لأن وصفه طويل جداً. (تم إعلام المسؤول)",
                "bad_image": f"❌ فشل نشر المود (ID: {mod_id}) بسبب مشكلة في رابط الصورة المرفقة. (تم إعلام المسؤول)"
            },
            "en": {
                "bad_request": f"❌ Failed to publish mod (ID: {mod_id}) to channel {channel_id} due to a bad request. ({e.message})",
                "chat_not_found_br": f"❌ Failed to publish mod (ID: {mod_id}) to channel {channel_id} because the channel was not found (maybe deleted or ID changed).",
                "caption_too_long": f"❌ Failed to publish mod (ID: {mod_id}) because its description is too long. (Admin notified)",
                "bad_image": f"❌ Failed to publish mod (ID: {mod_id}) due to an issue with the attached image URL. (Admin notified)"
            }
        }
        user_message = texts.get(lang, texts['ar']).get(error_key, texts.get(lang, texts['ar'])['bad_request'])
        try:
            await context.bot.send_message(chat_id=user_id_str, text=user_message)
            # إعلام المسؤول بأخطاء المحتوى التي لا يستطيع المستخدم حلها
            if error_key in ["caption_too_long", "bad_image"]:
                 await send_notification(f"⚠️ فشل النشر للمستخدم {user_id_str} (القناة {channel_id}) للمود {mod_id} بسبب: {user_message}", context)
        except Exception as notify_err:
            logger.error(f"Failed to notify user {user_id_str} about publishing error (BadRequest): {notify_err}")
        save_published_mod(user_id_str, mod_id) # علم المود كمعالج لهذا المستخدم
        return False

    except (NetworkError, TimedOut) as e:
         logger.warning(f"Publish failed (Network/Timeout) for mod {mod_id} to channel {channel_id} (User: {user_id_str}): {e}")
         # عادة لا نعلم المستخدم بأخطاء الشبكة المؤقتة إلا إذا تكررت
         # يمكنك إضافة منطق هنا لتتبع الأخطاء المتكررة لنفس المستخدم/القناة
         # save_published_mod(user_id_str, mod_id) # لا تعلمه كمعالج عند فشل الشبكة المؤقت؟ قرار تصميمي. الأفضل عدم تعليمه.
         return False # فشل مؤقت، سيحاول مرة أخرى في الدورة القادمة

    except TelegramError as e: # Catch other Telegram errors
         logger.error(f"Publish failed (TelegramError) for mod {mod_id} to channel {channel_id} (User: {user_id_str}): {e}")
         texts = {
             "ar": f"❌ فشل النشر في القناة {channel_id} للمود (ID: {mod_id}) بسبب خطأ غير محدد من تيليجرام.",
             "en": f"❌ Failed to publish mod (ID: {mod_id}) to channel {channel_id} due to an unspecified Telegram error."
         }
         user_message = texts.get(lang, texts['ar'])
         try:
             await context.bot.send_message(chat_id=user_id_str, text=user_message)
         except Exception as notify_err:
             logger.error(f"Failed to notify user {user_id_str} about publishing error (TelegramError): {notify_err}")
         save_published_mod(user_id_str, mod_id) # علم المود كمعالج
         return False

    except Exception as e: # Catch any other unexpected errors
         logger.critical(f"CRITICAL Publish failed (Exception) for mod {mod_id} to channel {channel_id} (User: {user_id_str}): {e}", exc_info=True)
         texts = {
             "ar": f"❌ حدث خطأ فادح وغير متوقع أثناء محاولة نشر المود (ID: {mod_id}) في القناة {channel_id}.",
             "en": f"❌ A critical unexpected error occurred while trying to publish mod (ID: {mod_id}) to channel {channel_id}."
         }
         user_message = texts.get(lang, texts['ar'])
         try:
             # إعلام المستخدم بالخطأ الفادح
             await context.bot.send_message(chat_id=user_id_str, text=user_message)
             # إعلام المسؤول بالتفاصيل
             await send_notification(f"🚨 خطأ فادح أثناء النشر للمستخدم {user_id_str} (القناة {channel_id}) للمود {mod_id}:\n<pre>{html.escape(str(e))}</pre>", context)
         except Exception as notify_err:
             logger.error(f"Failed to notify user/admin about critical publishing error: {notify_err}")
         save_published_mod(user_id_str, mod_id) # علم المود كمعالج
         return False

    # --- معالجة الأخطاء (كما هي) ---
    except BadRequest as e:
        # ... (error handling) ...
        return False
    except TelegramError as e:
         # ... (error handling) ...
         return False
    except Exception as e:
         # ... (error handling) ...
         return False

# --- Background Jobs ---

async def check_users_and_propose_mods(context: ContextTypes.DEFAULT_TYPE):
    """
    Checks active users' schedules. If due, finds a new unpublished mod.
    If admin preview is required, proposes it to the admin for that user.
    If admin preview is NOT required, sends directly to user (preview or publish).
    """
    logger.info("--- Running check_users_and_propose_mods job ---")
    user_channels_data = load_user_channels()
    pending_list = load_pending_publications()
    now = datetime.now(timezone.utc)
    processed_user_count = 0
    proposed_or_sent_count = 0 # Renamed for clarity
    max_actions_per_run = 10 # Limit total proposals OR direct sends per run

    # <<< --- START: Check Admin Preview Requirement --- >>>
    admin_needs_preview = is_admin_preview_required()
    logger.info(f"Admin preview required setting: {admin_needs_preview}")
    # <<< --- END: Check Admin Preview Requirement --- >>>

    # Create a set of users who already have a mod awaiting approval (admin OR user)
    users_awaiting_approval = {
        item["user_id"] for item in pending_list
        if item.get("status") in ["awaiting_admin_approval", "awaiting_user_approval"]
    }
    logger.debug(f"Users currently awaiting any approval: {users_awaiting_approval}")

    user_items = list(user_channels_data.items())
    random.shuffle(user_items)

    for user_id_str, user_data in user_items:
        processed_user_count += 1
        logger.debug(f"Checking user {user_id_str}...")

        # Basic User Eligibility Checks (same as before)
        if not isinstance(user_data, dict): continue
        if not user_data.get("active", True): continue
        channel_id = user_data.get("channel_id")
        if not channel_id: continue
        if user_id_str in users_awaiting_approval:
            logger.debug(f"Skipping user {user_id_str}: Already has a mod awaiting approval (admin or user).")
            continue
        if proposed_or_sent_count >= max_actions_per_run:
             logger.info(f"Reached max actions ({max_actions_per_run}) for this run.")
             break

        # Check if it's time to publish for this user (interval-based)
        interval_minutes = user_data.get("publish_interval", 60)
        last_publish_time_str = user_data.get("last_publish_time")

        is_due = False
        if last_publish_time_str is None:
            is_due = True # First time posting
        else:
            try:
                last_publish_time = datetime.fromisoformat(last_publish_time_str)
                if last_publish_time.tzinfo is None:
                    last_publish_time = last_publish_time.replace(tzinfo=timezone.utc)

                next_publish_time = last_publish_time + timedelta(minutes=interval_minutes)
                if now >= next_publish_time:
                    is_due = True
            except ValueError:
                logger.error(f"Invalid last_publish_time format for user {user_id_str}: {last_publish_time_str}. Assuming due.")
                is_due = True


        # --- Action if Due ---
        if is_due:
            logger.info(f"User {user_id_str} is due for a new mod. Checking permissions and finding mod...")

            permissions_ok = await check_channel_permissions(context.bot, channel_id, user_id_str)
            if not permissions_ok:
                logger.warning(f"Permissions check failed for user {user_id_str} (channel {channel_id}) when due. Skipping.")
                continue

            lang = user_data.get("lang", "ar")
            next_mod = get_next_random_unpublished_mod(user_id_str, lang)

            if next_mod:
                mod_id = next_mod['id'] # Get mod ID for logging/actions

                # <<< --- START: Conditional Logic based on Admin Setting --- >>>
                if admin_needs_preview:
                    # --- Path 1: Admin Preview Required (Existing Logic) ---
                    logger.info(f"Found mod {mod_id}. Admin preview IS required. Proposing to admin for user {user_id_str}...")
                    await preview_mod_for_user_approval(next_mod, user_id_str, context)
                    # Add user to the set locally (important!)
                    users_awaiting_approval.add(user_id_str)
                    proposed_or_sent_count += 1
                    await asyncio.sleep(1.0) # Delay

                else:
                    # --- Path 2: Admin Preview NOT Required ---
                    logger.info(f"Found mod {mod_id}. Admin preview is NOT required. Processing directly for user {user_id_str}...")
                    user_needs_preview = user_data.get("preview_enabled", False)

                    if user_needs_preview:
                        # --- Path 2a: Send Preview Directly to User ---
                        logger.info(f"User {user_id_str} has preview enabled. Attempting to add mod {mod_id} to pending (awaiting user) and send preview.")

                        # --- <<< START FIX >>> ---
                        # 1. Add to pending queue with 'awaiting_user_approval' status FIRST
                        added_to_pending = add_awaiting_user_approval(mod_id, user_id_str, channel_id)

                        if added_to_pending:
                            # 2. Send the preview to the user
                            # This function sends the preview message with Approve/Reject/Block buttons
                            asyncio.create_task(send_single_user_preview(context, user_id_str, next_mod, lang))
                            logger.info(f"Scheduled user preview task for mod {mod_id} / user {user_id_str}")

                            # 3. Update last_publish_time to respect interval AFTER sending preview
                            now_iso = datetime.now(timezone.utc).isoformat()
                            update_success = update_publish_settings(int(user_id_str), last_publish_time_str=now_iso)
                            if update_success:
                                logger.info(f"Updated last_publish_time for user {user_id_str} after scheduling user preview.")
                            else:
                                logger.warning(f"Failed to update last_publish_time for user {user_id_str} after scheduling preview.")

                            # 4. Update local tracking and counts
                            users_awaiting_approval.add(user_id_str) # Mark user as awaiting
                            proposed_or_sent_count += 1
                            await asyncio.sleep(1.0) # Delay
                        else:
                            logger.error(f"Failed to add mod {mod_id} to pending (awaiting user) for user {user_id_str}. Preview not sent.")
                            # Do not update last_publish_time if adding to pending failed
                        # --- <<< END FIX >>> ---

                    else:
                        # --- Path 2b: Publish Directly to User's Channel ---
                        logger.info(f"User {user_id_str} has preview disabled. Attempting direct publish for mod {mod_id}.")
                        publish_success = await publish_single_mod(mod_id, user_id_str, context)
                        if publish_success:
                             logger.info(f"Direct publish of mod {mod_id} successful for user {user_id_str}.")
                             proposed_or_sent_count += 1 # Count successful direct publish
                             await asyncio.sleep(0.5) # Shorter delay after publish
                        else:
                             logger.error(f"Direct publish of mod {mod_id} FAILED for user {user_id_str}. Mod marked as processed for user.")
                             # publish_single_mod already marks as processed on failure inside it
                             # No need to increment proposed_or_sent_count here
                # <<< --- END: Conditional Logic --- >>>

            else:
                logger.info(f"User {user_id_str} is due, but no new unpublished mods found for language '{lang}'.")

    logger.info(f"--- Finished check_users_and_propose_mods job. Checked: {processed_user_count}, Proposed/Sent: {proposed_or_sent_count} ---")

# Removed send_historical_mods - too complex and potentially resource-intensive for now.

async def fetch_and_preview_batch(context: ContextTypes.DEFAULT_TYPE):
    """
    (OBSOLETE for main preview flow) - Checks for globally unprocessed mods.
    Can optionally notify the admin if new files are found.
    Preview sending from this function is disabled in the new user-centric flow.
    """
    if not YOUR_CHAT_ID: # Ensure admin is set
        logger.error("fetch_and_preview_batch: Admin Chat ID not configured. Skipping.")
        return

    try:
        logger.debug("Running fetch_and_preview_batch job (Note: General preview sending is disabled)...")

        # --- تعديل: استدعاء load_mods() بدون وسيط لغة ---
        all_mods = load_mods() # تحميل جميع المودات من الملف الموحد
        # -----------------------------------------------

        # بناء قاموس ومعرفات من المودات المحملة
        all_mods_dict = {mod['id']: mod for mod in all_mods}
        all_mod_ids = set(all_mods_dict.keys())

        if not all_mod_ids:
            # يمكنك إزالة التعليق إذا أردت رؤية هذه الرسالة في السجلات
            # logger.info("fetch_and_preview_batch: No mods found in the mods file.")
            return

        # Load mods already processed by admin (using the general admin_processed_mods file)
        # This still helps identify truly 'new' mods added to the source files.
        admin_processed_ids = load_admin_processed_mods()

        # Find mods that are available and *not* processed by admin yet globally
        available_unprocessed_ids = list(all_mod_ids - admin_processed_ids)

        if not available_unprocessed_ids:
            # يمكنك إزالة التعليق إذا أردت رؤية هذه الرسالة
            # logger.info("fetch_and_preview_batch: All available mods seem to have been processed globally at some point.")
            return

        # --- Preview Sending DISABLED ---
        # The following block remains commented out as previews are handled user-specifically.
        # You can uncomment the log line if you want to see which mods are found.
        # batch_size = 5 # Fetch 5 new mods per run (adjustable)
        # num_to_select = min(batch_size, len(available_unprocessed_ids))
        # import random
        # selected_mod_ids = random.sample(available_unprocessed_ids, num_to_select)

        logger.info(f"fetch_and_preview_batch: Found {len(available_unprocessed_ids)} mods not yet processed globally: {available_unprocessed_ids}. (General preview sending is disabled)")

        # --- DISABLED LOOP TO SEND GENERAL PREVIEWS ---
        # for mod_id in selected_mod_ids:
        #     mod_data = all_mods_dict.get(mod_id)
        #     if mod_data:
                 # DISABLED: await preview_mod_before_publish(mod_data, context) # This sends the OLD general preview
                 # await asyncio.sleep(0.5)
        #     else:
        #          logger.warning(f"fetch_and_preview_batch: Mod data for ID {mod_id} not found unexpectedly.")
        # --- END DISABLED LOOP ---

        # --- Optional: Notify admin about new unprocessed mods (Keep commented if not needed) ---
        # try:
        #    # Check if we already notified recently to avoid spamming
        #    last_notified_count = context.bot_data.get('last_unprocessed_notify_count', -1)
        #    current_count = len(available_unprocessed_ids)
        #    if current_count > 0 and current_count != last_notified_count:
        #         await send_notification(f"ℹ️ تم اكتشاف {current_count} مودات جديدة لم تتم معالجتها عالمياً بعد.", context)
        #         context.bot_data['last_unprocessed_notify_count'] = current_count
        #    elif current_count == 0: # Reset if count goes to 0
        #         context.bot_data['last_unprocessed_notify_count'] = 0
        # except Exception as notify_err:
        #     logger.error(f"Error sending notification about unprocessed mods: {notify_err}")
        # --- End Optional Notification ---

    # --- تعديل: التأكد من معالجة الخطأ بشكل صحيح ---
    except TypeError as e: # التقاط الخطأ المحدد الذي حدث سابقاً
        logger.error(f"Error in fetch_and_preview_batch (likely related to load_mods call): {str(e)}", exc_info=True)
        # يمكنك إرسال إشعار للمسؤول هنا إذا كان هذا الخطأ حرجاً
        # await send_notification(f"⚠️ خطأ في وظيفة فحص المودات الجديدة (fetch_and_preview_batch - TypeError): <pre>{html.escape(str(e))}</pre>", context)
    except Exception as e:
        logger.error(f"خطأ في fetch_and_preview_batch: {str(e)}", exc_info=True)
        # تجنب إرسال الإشعارات عن الأخطاء العامة في هذه الوظيفة إذا لم تكن حرجة
        # await send_notification(f"⚠️ خطأ في وظيفة فحص المودات الجديدة (fetch_and_preview_batch): <pre>{html.escape(str(e))}</pre>", context)

async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE):
    """Log Errors caused by Updates."""
    logger.error(f"Update {update} caused error: {context.error}", exc_info=context.error)

    # More specific error handling can be added here
    if isinstance(context.error, BadRequest):
        if "message is too long" in str(context.error).lower():
             logger.error("Error: Message too long. Check mod description length.")
             # Notify admin if possible
             if isinstance(update, Update) and update.effective_chat:
                  await context.bot.send_message(update.effective_chat.id, "⚠️ حدث خطأ: وصف أحد المودات طويل جداً.")
        # Add handling for other specific BadRequest errors if needed
    elif isinstance(context.error, TelegramError):
        logger.warning(f"Telegram API Error: {context.error}")
        # Handle specific Telegram errors like network issues, timeouts?
    else:
         # Notify admin about unexpected errors
         try:
             await send_notification(
                 f"🚨 خطأ غير متوقع في البوت:\n"
                 f"<pre>{html.escape(str(context.error))}</pre>\n"
                 f"Update: <pre>{html.escape(str(update))}</pre>",
                 context
             )
         except Exception as notify_err:
              logger.error(f"Failed to send error notification to admin: {notify_err}")


# --- Main Execution ---

# --- Main Execution ---

def main():
    """Run the bot with user-centric proposal, broadcast, and force-enable features.""" # Updated docstring
    logger.info("🔧 Initializing files...")
    initialize_files()
    logger.info("🚀 Starting bot...")

    # Validate essential config
    if not TOKEN or not YOUR_CHAT_ID:
         logger.critical("🔥 BOT_TOKEN or ADMIN_CHAT_ID not set! Exiting.")
         return
    try:
        admin_id_int = int(YOUR_CHAT_ID) # Convert admin ID once for filters
    except ValueError:
        logger.critical(f"🔥 ADMIN_CHAT_ID ('{YOUR_CHAT_ID}') is not a valid integer! Admin features might not work correctly. Exiting.")
        return

    try:
        # Create application with simplified configuration
        application = (
            Application.builder()
            .token(TOKEN)
            .build()
        )

        # ========== Command Handlers ==========
        application.add_handler(CommandHandler("start", start))
        application.add_handler(CommandHandler("menu", show_main_menu))
        application.add_handler(CommandHandler("status", status))
        application.add_handler(CommandHandler("set_interval", set_publish_interval))
        application.add_handler(CommandHandler("admin", admin_panel, filters=filters.User(user_id=admin_id_int)))
        application.add_handler(CommandHandler("help", help_menu))

        # ========== Callback Query Handlers ==========
        # ORDER MATTERS! More specific patterns should generally come first.

        # --- Language & Main Menu ---
        application.add_handler(CallbackQueryHandler(handle_language_selection, pattern="^lang_(ar|en)$"))
        application.add_handler(CallbackQueryHandler(handle_channel_link_continue, pattern="^channel_link_continue$"))
        application.add_handler(CallbackQueryHandler(handle_channel_link_skip, pattern="^channel_link_skip$"))

        # --- Mod Categories Management ---
        application.add_handler(CallbackQueryHandler(toggle_mod_category, pattern=r"^toggle_category_.+$"))
        application.add_handler(CallbackQueryHandler(select_all_categories, pattern="^select_all_categories$"))
        application.add_handler(CallbackQueryHandler(finish_mod_categories_selection, pattern="^finish_mod_categories_selection$"))
        application.add_handler(CallbackQueryHandler(manage_categories_from_settings, pattern="^setting_manage_categories$"))
        application.add_handler(CallbackQueryHandler(settings_toggle_category, pattern=r"^settings_toggle_category_.+$"))
        application.add_handler(CallbackQueryHandler(settings_select_all_categories, pattern="^settings_select_all_categories$"))
        application.add_handler(CallbackQueryHandler(settings_save_categories, pattern="^settings_save_categories$"))

        # --- Minecraft Versions Management ---
        application.add_handler(CallbackQueryHandler(toggle_mc_version, pattern=r"^toggle_version_.+$"))
        application.add_handler(CallbackQueryHandler(select_all_mc_versions, pattern="^select_all_versions$"))
        application.add_handler(CallbackQueryHandler(back_to_categories_from_versions, pattern="^back_to_categories$"))
        application.add_handler(CallbackQueryHandler(finish_mc_versions_selection, pattern="^finish_versions_selection$"))
        application.add_handler(CallbackQueryHandler(manage_versions_from_settings, pattern="^setting_manage_versions$"))
        application.add_handler(CallbackQueryHandler(settings_toggle_version, pattern=r"^settings_toggle_version_.+$"))
        application.add_handler(CallbackQueryHandler(settings_select_all_versions, pattern="^settings_select_all_versions$"))
        application.add_handler(CallbackQueryHandler(settings_save_versions, pattern="^settings_save_versions$"))

        # --- Message Format Management ---
        application.add_handler(CallbackQueryHandler(select_message_format, pattern=r"^select_format_.+$"))
        application.add_handler(CallbackQueryHandler(preview_message_format, pattern="^preview_format$"))
        application.add_handler(CallbackQueryHandler(back_to_versions_from_format, pattern="^back_to_versions$"))
        application.add_handler(CallbackQueryHandler(back_to_format_selection, pattern="^back_to_format_selection$"))
        application.add_handler(CallbackQueryHandler(finish_format_selection, pattern="^finish_format_selection$"))
        application.add_handler(CallbackQueryHandler(manage_format_from_settings, pattern="^setting_manage_format$"))
        application.add_handler(CallbackQueryHandler(settings_select_format, pattern=r"^settings_select_format_.+$"))
        application.add_handler(CallbackQueryHandler(settings_preview_format, pattern="^settings_preview_format$"))
        application.add_handler(CallbackQueryHandler(settings_save_format, pattern="^settings_save_format$"))

        # --- Channel Language Management ---
        application.add_handler(CallbackQueryHandler(select_channel_language, pattern=r"^select_channel_lang_.+$"))
        application.add_handler(CallbackQueryHandler(back_to_format_from_lang, pattern="^back_to_format$"))
        application.add_handler(CallbackQueryHandler(finish_channel_lang_selection, pattern="^finish_channel_lang_selection$"))
        application.add_handler(CallbackQueryHandler(manage_language_from_settings, pattern="^setting_manage_language$"))
        application.add_handler(CallbackQueryHandler(settings_select_language, pattern=r"^settings_select_lang_.+$"))
        application.add_handler(CallbackQueryHandler(settings_save_language, pattern="^settings_save_language$"))

        application.add_handler(CallbackQueryHandler(show_main_menu, pattern="^main_menu$"))
        application.add_handler(CallbackQueryHandler(change_language_menu, pattern="^change_language_menu$"))
        application.add_handler(CallbackQueryHandler(help_menu, pattern="^help_menu$"))

        # --- Channel Management & Setup ---
        application.add_handler(CallbackQueryHandler(manage_channel_menu, pattern="^manage_channel_menu$"))
        application.add_handler(CallbackQueryHandler(prompt_add_change_channel, pattern="^prompt_add_change_channel$"))
        application.add_handler(CallbackQueryHandler(handle_interval_selection, pattern=r"^interval_min_\d+$"))

        # --- User Channel Settings ---
        application.add_handler(CallbackQueryHandler(show_channel_settings_for_user, pattern="^show_channel_settings$"))
        application.add_handler(CallbackQueryHandler(toggle_publish_for_user, pattern="^setting_toggle_publish$"))
        application.add_handler(CallbackQueryHandler(toggle_preview_for_user, pattern="^setting_toggle_preview$"))
        application.add_handler(CallbackQueryHandler(change_interval_menu_for_user, pattern="^setting_change_interval_menu$"))
        application.add_handler(CallbackQueryHandler(set_interval_for_user, pattern=r"^setting_set_interval_\d+$"))
        application.add_handler(CallbackQueryHandler(delete_channel_confirm, pattern="^setting_delete_channel_confirm$"))
        application.add_handler(CallbackQueryHandler(delete_channel_execute, pattern="^setting_delete_channel_execute$"))



        # --- Admin Panel & Specific Actions ---
        # Handle very specific admin actions first
        application.add_handler(CallbackQueryHandler(admin_panel, pattern="^admin_panel$"))
        application.add_handler(CallbackQueryHandler(admin_toggle_preview_handler, pattern="^admin_toggle_preview$"))
        # --- <<< تسجيل معالج زر تفعيل النشر هنا >>> ---
        application.add_handler(CallbackQueryHandler(admin_force_enable_publish_handler, pattern="^admin_force_enable_all_publish$")) # <-- تأكد من وجود هذا السطر
        # --- <<< نهاية التسجيل >>> ---
        application.add_handler(CallbackQueryHandler(show_admin_stats, pattern="^admin_stats$"))
        application.add_handler(CallbackQueryHandler(admin_restart_publishing_handler, pattern="^admin_restart_publishing$"))
        application.add_handler(CallbackQueryHandler(admin_broadcast_menu_handler, pattern="^admin_broadcast_menu$"))
        application.add_handler(CallbackQueryHandler(show_user_list_for_admin, pattern="^admin_list_users$")) # Initial view
        application.add_handler(CallbackQueryHandler(show_mod_list_for_admin, pattern="^admin_republish_list$")) # Initial view
        application.add_handler(CallbackQueryHandler(show_mod_list_for_admin, pattern="^admin_delete_mod_list$")) # Initial view
        application.add_handler(CallbackQueryHandler(admin_panel, pattern="^back_to_admin$")) # Back button to admin panel

        # --- Admin Broadcast Flow ---
        application.add_handler(CallbackQueryHandler(admin_broadcast_target_handler, pattern="^broadcast_target_(all|ar|en|specific)$"))
        application.add_handler(CallbackQueryHandler(admin_broadcast_cancel_handler, pattern="^broadcast_cancel$"))

        # --- Admin User Management Flow (Pagination, Details, Delete) ---
        application.add_handler(CallbackQueryHandler(handle_admin_user_list_page, pattern=r"^admin_list_users_\d+$")) # Pagination
        application.add_handler(CallbackQueryHandler(admin_view_user_details, pattern=r"^admin_view_user_\d+$"))
        application.add_handler(CallbackQueryHandler(admin_delete_user_confirmation, pattern=r"^admin_delete_confirm_\d+$"))
        application.add_handler(CallbackQueryHandler(admin_delete_user_execute, pattern=r"^admin_delete_execute_\d+$"))

        # --- Admin Mod Management Flow (Pagination, Actions) ---
        # Note: show_mod_list_for_admin is now called directly from the initial handlers above
        application.add_handler(CallbackQueryHandler(handle_admin_mod_list_page, pattern=r"^(republish|delete_mod)_list_\d+$")) # Pagination
        application.add_handler(CallbackQueryHandler(republish_mod_handler, pattern=r"^republish_\d+")) # Action: republish
        # The delete_mod pattern handles the confirmation trigger
        application.add_handler(CallbackQueryHandler(delete_mod_confirmation, pattern=r"^delete_mod_\d+"))
        application.add_handler(CallbackQueryHandler(delete_mod_execute, pattern=r"^delete_mod_execute_\d+$")) # Action: delete execute

        # --- Admin/User Preview Flow ---
        application.add_handler(CallbackQueryHandler(handle_preview_response, pattern=r"^admin_(publish|skip|approve_user|skip_user)_"))
        application.add_handler(CallbackQueryHandler(handle_user_preview_response, pattern=r"^user_(approve|reject)_\d+"))
        application.add_handler(CallbackQueryHandler(handle_user_block_response, pattern=r"^user_block_\d+"))

        # ========== Message Handlers ==========
        # Group -1: Highest priority for initial channel setup
        application.add_handler(MessageHandler(
            filters.ChatType.PRIVATE & (filters.TEXT | filters.FORWARDED) & ~filters.COMMAND & filters.UpdateType.MESSAGE,
            handle_channel_input
        ), group=-1)

        # Group 1: Catches admin input for broadcast feature
        admin_filter = filters.User(user_id=admin_id_int) & filters.ChatType.PRIVATE
        application.add_handler(MessageHandler(
            admin_filter & filters.UpdateType.MESSAGE & ~filters.COMMAND, # Any message type from admin, not a command
            admin_handle_broadcast_input
        ), group=1)

        # ========== Error Handler ==========
        application.add_error_handler(error_handler)

        # ========== Job Queue ==========
        job_queue = application.job_queue
        job_queue.run_repeating(check_users_and_propose_mods, interval=timedelta(minutes=1), first=timedelta(seconds=10), name="check_users_propose_send")
        job_queue.run_repeating(fetch_and_preview_batch, interval=timedelta(minutes=15), first=timedelta(seconds=30), name="check_new_files")
        job_queue.run_repeating(cleanup_stale_pending_items, interval=timedelta(hours=PENDING_CLEANUP_INTERVAL_HOURS), first=timedelta(minutes=5), name="cleanup_pending")


        logger.info("✅ Bot setup complete with broadcast and force-enable features. Starting polling...")
        application.run_polling(drop_pending_updates=True, allowed_updates=Update.ALL_TYPES)

    except ValueError as ve:
         logger.critical(f"🔥 Configuration Error: {ve}")
    except Exception as e:
        logger.critical(f"🔥 Bot failed to start: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    main()

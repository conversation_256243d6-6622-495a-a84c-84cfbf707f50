import logging
import requests
from typing import List, Dict, Optional

# إعداد التسجيل
logger = logging.getLogger(__name__)

# إعدادات Supabase
SUPABASE_URL = "https://wnjbrdubiegjuycumuoh.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InduamJyZHViaWVnanV5Y3VtdW9oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5NzAyNTcsImV4cCI6MjA2MjU0NjI1N30.iR00a54EoU4jwUrIQRxS2uqDjkcu_YovYWityww7zIc"

# إعداد headers للطلبات
HEADERS = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json',
    'Prefer': 'return=representation'
}

def get_all_mods() -> List[Dict]:
    """
    جلب جميع المودات من قاعدة البيانات Supabase
    Returns: قائمة بجميع المودات
    """
    try:
        logger.info("جاري جلب جميع المودات من Supabase...")

        # جلب البيانات من جدول minemods
        url = f"{SUPABASE_URL}/rest/v1/minemods"
        response = requests.get(url, headers=HEADERS)

        if response.status_code == 200:
            data = response.json()
            mods = []
            for row in data:
                # تحويل البيانات من قاعدة البيانات إلى التنسيق المطلوب
                mod = {
                    'id': row['id'],
                    'title': row['name'] or 'Untitled Mod',
                    'description': {
                        'ar': row['description'] or 'لا يوجد وصف',
                        'en': row['description'] or 'No description available'
                    },
                    'download_url': row['download_link'],
                    'image_url': row['image_path'],
                    'version': row['mc_version'],
                    'mod_type': 'mod',  # افتراضي
                    'for': row['mod_loader'] or 'minecraft'
                }

                # إضافة بيانات إضافية من mod_data_json إذا وجدت
                if row.get('mod_data_json'):
                    mod_data = row['mod_data_json']
                    if isinstance(mod_data, dict):
                        # تحديث الوصف إذا كان متوفراً في mod_data_json
                        if 'description_ar' in mod_data:
                            mod['description']['ar'] = mod_data['description_ar']
                        if 'description_en' in mod_data:
                            mod['description']['en'] = mod_data['description_en']
                        if 'mod_type' in mod_data:
                            mod['mod_type'] = mod_data['mod_type']

                mods.append(mod)

            logger.info(f"تم جلب {len(mods)} مود من قاعدة البيانات")
            return mods
        else:
            logger.warning(f"فشل في جلب المودات: {response.status_code} - {response.text}")
            return []

    except Exception as e:
        logger.error(f"خطأ في جلب المودات من Supabase: {e}")
        return []

def get_mod_by_id(mod_id: int) -> Optional[Dict]:
    """
    جلب مود واحد بواسطة المعرف
    Args:
        mod_id: معرف المود
    Returns: بيانات المود أو None إذا لم يوجد
    """
    try:
        logger.info(f"جاري جلب المود بالمعرف {mod_id} من Supabase...")

        # جلب البيانات من جدول minemods
        url = f"{SUPABASE_URL}/rest/v1/minemods?id=eq.{mod_id}"
        response = requests.get(url, headers=HEADERS)

        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                row = data[0]
                mod = {
                    'id': row['id'],
                    'title': row['name'] or 'Untitled Mod',
                    'description': {
                        'ar': row['description'] or 'لا يوجد وصف',
                        'en': row['description'] or 'No description available'
                    },
                    'download_url': row['download_link'],
                    'image_url': row['image_path'],
                    'version': row['mc_version'],
                    'mod_type': 'mod',
                    'for': row['mod_loader'] or 'minecraft'
                }

                # إضافة بيانات إضافية من mod_data_json إذا وجدت
                if row.get('mod_data_json'):
                    mod_data = row['mod_data_json']
                    if isinstance(mod_data, dict):
                        if 'description_ar' in mod_data:
                            mod['description']['ar'] = mod_data['description_ar']
                        if 'description_en' in mod_data:
                            mod['description']['en'] = mod_data['description_en']
                        if 'mod_type' in mod_data:
                            mod['mod_type'] = mod_data['mod_type']

                logger.info(f"تم جلب المود {mod_id} بنجاح")
                return mod
            else:
                logger.warning(f"لم يتم العثور على المود بالمعرف {mod_id}")
                return None
        else:
            logger.warning(f"فشل في جلب المود {mod_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في جلب المود {mod_id} من Supabase: {e}")
        return None

def delete_mod_from_db(mod_id: int) -> bool:
    """
    حذف مود من قاعدة البيانات
    Args:
        mod_id: معرف المود المراد حذفه
    Returns: True إذا تم الحذف بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري حذف المود {mod_id} من Supabase...")

        url = f"{SUPABASE_URL}/rest/v1/minemods?id=eq.{mod_id}"
        response = requests.delete(url, headers=HEADERS)

        if response.status_code == 200:
            logger.info(f"تم حذف المود {mod_id} بنجاح من قاعدة البيانات")
            return True
        elif response.status_code == 404:
            logger.warning(f"لم يتم العثور على المود {mod_id} للحذف")
            return False
        else:
            logger.warning(f"فشل في حذف المود {mod_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في حذف المود {mod_id} من Supabase: {e}")
        return False

def add_mod_to_db(mod_data: Dict) -> bool:
    """
    إضافة مود جديد إلى قاعدة البيانات
    Args:
        mod_data: بيانات المود
    Returns: True إذا تم الإضافة بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري إضافة مود جديد إلى Supabase...")

        # تحضير البيانات للإدراج
        insert_data = {
            'name': mod_data.get('title', 'Untitled Mod'),
            'description': mod_data.get('description', {}).get('ar', 'لا يوجد وصف'),
            'image_path': mod_data.get('image_url'),
            'download_link': mod_data.get('download_url'),
            'mc_version': mod_data.get('version'),
            'mod_loader': mod_data.get('for', 'minecraft'),
            'mod_data_json': {
                'description_ar': mod_data.get('description', {}).get('ar', ''),
                'description_en': mod_data.get('description', {}).get('en', ''),
                'mod_type': mod_data.get('mod_type', 'mod')
            }
        }

        url = f"{SUPABASE_URL}/rest/v1/minemods"
        response = requests.post(url, headers=HEADERS, json=insert_data)

        if response.status_code == 201:
            logger.info(f"تم إضافة المود الجديد بنجاح")
            return True
        else:
            logger.error(f"فشل في إضافة المود الجديد: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في إضافة المود إلى Supabase: {e}")
        return False

def update_mod_in_db(mod_id: int, mod_data: Dict) -> bool:
    """
    تحديث مود موجود في قاعدة البيانات
    Args:
        mod_id: معرف المود
        mod_data: البيانات الجديدة
    Returns: True إذا تم التحديث بنجاح، False إذا فشل
    """
    try:
        logger.info(f"جاري تحديث المود {mod_id} في Supabase...")

        # تحضير البيانات للتحديث
        update_data = {
            'name': mod_data.get('title'),
            'description': mod_data.get('description', {}).get('ar'),
            'image_path': mod_data.get('image_url'),
            'download_link': mod_data.get('download_url'),
            'mc_version': mod_data.get('version'),
            'mod_loader': mod_data.get('for'),
            'mod_data_json': {
                'description_ar': mod_data.get('description', {}).get('ar', ''),
                'description_en': mod_data.get('description', {}).get('en', ''),
                'mod_type': mod_data.get('mod_type', 'mod')
            }
        }

        # إزالة القيم الفارغة
        update_data = {k: v for k, v in update_data.items() if v is not None}

        url = f"{SUPABASE_URL}/rest/v1/minemods?id=eq.{mod_id}"
        response = requests.patch(url, headers=HEADERS, json=update_data)

        if response.status_code == 200:
            logger.info(f"تم تحديث المود {mod_id} بنجاح")
            return True
        elif response.status_code == 404:
            logger.warning(f"لم يتم العثور على المود {mod_id} للتحديث")
            return False
        else:
            logger.warning(f"فشل في تحديث المود {mod_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في تحديث المود {mod_id} في Supabase: {e}")
        return False

def get_mods_count() -> int:
    """
    الحصول على عدد المودات في قاعدة البيانات
    Returns: عدد المودات
    """
    try:
        url = f"{SUPABASE_URL}/rest/v1/minemods?select=id"
        headers_with_count = HEADERS.copy()
        headers_with_count['Prefer'] = 'count=exact'

        response = requests.head(url, headers=headers_with_count)

        if response.status_code == 200:
            count_header = response.headers.get('Content-Range', '0')
            # Content-Range format: "0-1/2" where 2 is the total count
            if '/' in count_header:
                return int(count_header.split('/')[-1])
            return 0
        else:
            logger.warning(f"فشل في الحصول على عدد المودات: {response.status_code}")
            return 0
    except Exception as e:
        logger.error(f"خطأ في الحصول على عدد المودات من Supabase: {e}")
        return 0

def search_mods(search_term: str) -> List[Dict]:
    """
    البحث عن المودات بالاسم أو الوصف
    Args:
        search_term: مصطلح البحث
    Returns: قائمة بالمودات المطابقة
    """
    try:
        logger.info(f"البحث عن المودات بالمصطلح: {search_term}")

        # البحث في الاسم والوصف
        url = f"{SUPABASE_URL}/rest/v1/minemods?or=(name.ilike.*{search_term}*,description.ilike.*{search_term}*)"
        response = requests.get(url, headers=HEADERS)

        if response.status_code == 200:
            data = response.json()
            mods = []
            for row in data:
                mod = {
                    'id': row['id'],
                    'title': row['name'] or 'Untitled Mod',
                    'description': {
                        'ar': row['description'] or 'لا يوجد وصف',
                        'en': row['description'] or 'No description available'
                    },
                    'download_url': row['download_link'],
                    'image_url': row['image_path'],
                    'version': row['mc_version'],
                    'mod_type': 'mod',
                    'for': row['mod_loader'] or 'minecraft'
                }

                if row.get('mod_data_json'):
                    mod_data = row['mod_data_json']
                    if isinstance(mod_data, dict):
                        if 'description_ar' in mod_data:
                            mod['description']['ar'] = mod_data['description_ar']
                        if 'description_en' in mod_data:
                            mod['description']['en'] = mod_data['description_en']
                        if 'mod_type' in mod_data:
                            mod['mod_type'] = mod_data['mod_type']

                mods.append(mod)

            logger.info(f"تم العثور على {len(mods)} مود مطابق للبحث")
            return mods
        else:
            logger.warning(f"فشل في البحث عن المودات: {response.status_code} - {response.text}")
            return []

    except Exception as e:
        logger.error(f"خطأ في البحث عن المودات في Supabase: {e}")
        return []
